{"permissions": {"allow": ["mcp__vertex-ai-server__explain_topic_with_docs", "mcp__vertex-ai-server__answer_query_websearch", "mcp__gcp-mcp__list-projects", "mcp__gcp-mcp__select-project", "mcp__vertex-ai-server__answer_query_websearch", "mcp__gcp-mcp__run-gcp-code", "mcp__vertex-ai-server__get_directory_tree", "mcp__sequential-thinking__sequentialthinking", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "mcp__vertex-ai-server__get_directory_tree", "mcp__vertex-ai-server__get_doc_snippets", "mcp__vertex-ai-server__read_file_content", "mcp__vertex-ai-server__list_directory_contents", "mcp__vertex-ai-server__read_file_content", "mcp__vertex-ai-server__list_directory_contents"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["filesystem", "memory", "puppeteer", "sequential-thinking", "fetch", "gcp-mcp"], "disabledMcpjsonServers": ["github", "google", "postgres"]}