use axum::{
    extract::{ws::{WebSocket, WebSocketUpgrade, Message}, Path, State},
    response::IntoResponse,
};
use crate::api::AppState;
use std::sync::Arc;
use crate::models::{ProgressUpdate, AnalysisStatus};
use chrono::Utc;
use tokio::select;
use std::time::Duration;
use tracing::{debug, error, info};
use serde_json::json;

// WebSocket /ws/analysis/{id} - Real-time progress updates
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    Path(analysis_id): Path<String>,
    State(state): State<Arc<AppState>>,
) -> impl IntoResponse {
    ws.on_upgrade(move |socket| handle_socket(socket, analysis_id, state))
}

async fn handle_socket(mut socket: WebSocket, analysis_id: String, state: Arc<AppState>) {
    info!("WebSocket connection established for analysis: {}", analysis_id);
    
    // Check if analysis exists
    let analysis_exists = match &state.spanner_pool {
        Some(spanner) => match spanner.get_analysis(&analysis_id).await {
            Ok(Some(analysis)) => {
                // Send initial status
                let initial_update = ProgressUpdate {
                    analysis_id: analysis_id.clone(),
                    progress: analysis.progress.unwrap_or(0.0),
                    stage: analysis.current_stage.unwrap_or_else(|| "Initializing".to_string()),
                    message: Some(format!("Connected to analysis progress stream. Status: {:?}", analysis.status)),
                    timestamp: Utc::now(),
                    files_processed: analysis.performance_metrics.as_ref()
                        .map(|m| m.files_analyzed as usize),
                    total_files: Some(analysis.file_count),
                };

                if let Ok(json) = serde_json::to_string(&initial_update) {
                    if socket.send(Message::Text(json.into())).await.is_err() {
                        error!("Failed to send initial update for analysis: {}", analysis_id);
                        return;
                    }
                }
                
                matches!(analysis.status, AnalysisStatus::Pending | AnalysisStatus::InProgress)
            }
            Ok(None) => {
                // Analysis not found
                let _ = socket.send(Message::Text(
                    json!({"error": "Analysis not found", "analysis_id": analysis_id}).to_string().into()
                )).await;
                error!("Analysis not found: {}", analysis_id);
                return;
            }
            Err(e) => {
                // Database error
                let _ = socket.send(Message::Text(
                    json!({"error": "Failed to retrieve analysis", "details": e.to_string()}).to_string().into()
                )).await;
                error!("Failed to retrieve analysis {}: {}", analysis_id, e);
                return;
            }
        },
        None => {
            // Check if it's in active analyses (in-memory)
            if state.active_analyses.contains_key(&analysis_id) {
                // Send initial status for in-memory analysis
                let initial_update = ProgressUpdate {
                    analysis_id: analysis_id.clone(),
                    progress: 0.0,
                    stage: "Initializing".to_string(),
                    message: Some("Connected to analysis progress stream (in-memory mode)".to_string()),
                    timestamp: Utc::now(),
                    files_processed: None,
                    total_files: None,
                };

                if let Ok(json) = serde_json::to_string(&initial_update) {
                    if socket.send(Message::Text(json.into())).await.is_err() {
                        error!("Failed to send initial update for analysis: {}", analysis_id);
                        return;
                    }
                }
                true
            } else {
                let _ = socket.send(Message::Text(
                    json!({"error": "Database service unavailable and analysis not found in memory", "analysis_id": analysis_id}).to_string().into()
                )).await;
                error!("Database unavailable and analysis not found in memory: {}", analysis_id);
                return;
            }
        }
    };

    if !analysis_exists {
        return;
    }

    // Subscribe to progress updates for this analysis
    let mut progress_rx = state.progress_broadcast.subscribe();
    
    // Also set up a periodic check to ensure we don't miss status changes
    let mut check_interval = tokio::time::interval(Duration::from_secs(5));
    
    // Keep track of last known status to detect completion
    let mut last_status = AnalysisStatus::InProgress;
    
    loop {
        select! {
            // Receive real-time progress updates from the broadcast channel
            Ok(update) = progress_rx.recv() => {
                if update.analysis_id == analysis_id {
                    debug!("Sending progress update for analysis {}: {}% - {}", 
                        analysis_id, update.progress, update.stage);
                    
                    if let Ok(json) = serde_json::to_string(&update) {
                        if socket.send(Message::Text(json.into())).await.is_err() {
                            info!("Client disconnected for analysis: {}", analysis_id);
                            break;
                        }
                    }
                }
            }
            
            // Periodic check for analysis status
            _ = check_interval.tick() => {
                if let Some(pool) = &state.spanner_pool {
                    let spanner = pool.get().await.unwrap();
                    match spanner.get_analysis(&analysis_id).await {
                        Ok(Some(analysis)) => {
                            // Check if analysis completed
                            if matches!(analysis.status, AnalysisStatus::Completed | AnalysisStatus::Failed) 
                                && !matches!(last_status, AnalysisStatus::Completed | AnalysisStatus::Failed) {
                                
                                let final_update = ProgressUpdate {
                                    analysis_id: analysis_id.clone(),
                                    progress: if analysis.status == AnalysisStatus::Completed { 100.0 } else { analysis.progress.unwrap_or(0.0) },
                                    stage: match analysis.status {
                                        AnalysisStatus::Completed => "Completed".to_string(),
                                        AnalysisStatus::Failed => "Failed".to_string(),
                                        _ => unreachable!(),
                                    },
                                    message: Some(match analysis.status {
                                        AnalysisStatus::Completed => format!(
                                            "Analysis completed successfully. {} files processed, {} patterns detected",
                                            analysis.file_count,
                                            analysis.patterns.len()
                                        ),
                                        AnalysisStatus::Failed => format!(
                                            "Analysis failed: {}",
                                            analysis.error_message.unwrap_or_else(|| "Unknown error".to_string())
                                        ),
                                        _ => unreachable!(),
                                    }),
                                    timestamp: Utc::now(),
                                    files_processed: analysis.performance_metrics.as_ref()
                                        .map(|m| m.files_analyzed as usize),
                                    total_files: Some(analysis.file_count),
                                };
                                
                                if let Ok(json) = serde_json::to_string(&final_update) {
                                    let _ = socket.send(Message::Text(json.into())).await;
                                }
                                
                                info!("Analysis {} completed with status: {:?}", analysis_id, analysis.status);
                                break;
                            }
                            
                            last_status = analysis.status;
                            
                            // Send periodic status update even if no progress broadcast
                            if matches!(analysis.status, AnalysisStatus::InProgress) {
                                let status_update = ProgressUpdate {
                                    analysis_id: analysis_id.clone(),
                                    progress: analysis.progress.unwrap_or(0.0),
                                    stage: analysis.current_stage.unwrap_or_else(|| "Processing".to_string()),
                                    message: None,
                                    timestamp: Utc::now(),
                                    files_processed: analysis.performance_metrics.as_ref()
                                        .map(|m| m.files_analyzed as usize),
                                    total_files: Some(analysis.file_count),
                                };
                                
                                if let Ok(json) = serde_json::to_string(&status_update) {
                                    if socket.send(Message::Text(json.into())).await.is_err() {
                                        info!("Client disconnected during periodic update for analysis: {}", analysis_id);
                                        break;
                                    }
                                }
                            }
                        }
                        Ok(None) => {
                            // Analysis disappeared?
                            let _ = socket.send(Message::Text(
                                json!({"error": "Analysis no longer exists", "analysis_id": analysis_id}).to_string().into()
                            )).await;
                            break;
                        }
                        Err(e) => {
                            error!("Failed to check analysis status for {}: {}", analysis_id, e);
                            // Continue trying, might be temporary network issue
                        }
                    }
                } else {
                    // In-memory mode - check active analyses
                    if let Some(status) = state.active_analyses.get(&analysis_id) {
                        // Send periodic update for in-memory analysis
                        let status_update = ProgressUpdate {
                            analysis_id: analysis_id.clone(),
                            progress: 0.0, // We don't track progress in-memory
                            stage: format!("{:?}", status.value()),
                            message: Some("Running in memory mode".to_string()),
                            timestamp: Utc::now(),
                            files_processed: None,
                            total_files: None,
                        };
                        
                        if let Ok(json) = serde_json::to_string(&status_update) {
                            if socket.send(Message::Text(json.into())).await.is_err() {
                                info!("Client disconnected during periodic update for analysis: {}", analysis_id);
                                break;
                            }
                        }
                    } else {
                        // Analysis no longer in memory
                        let _ = socket.send(Message::Text(
                            json!({"error": "Analysis no longer exists in memory", "analysis_id": analysis_id}).to_string().into()
                        )).await;
                        break;
                    }
                }
            }
            
            // Handle incoming messages from client (ping/pong, close, etc.)
            Some(msg) = socket.recv() => {
                match msg {
                    Ok(Message::Text(text)) => {
                        debug!("Received text message from client: {}", text);
                        // Could implement client commands here
                    }
                    Ok(Message::Ping(data)) => {
                        if socket.send(Message::Pong(data)).await.is_err() {
                            break;
                        }
                    }
                    Ok(Message::Close(_)) => {
                        info!("Client requested close for analysis: {}", analysis_id);
                        break;
                    }
                    Err(e) => {
                        error!("WebSocket error for analysis {}: {}", analysis_id, e);
                        break;
                    }
                    _ => {}
                }
            }
        }
    }
    
    info!("WebSocket connection closed for analysis: {}", analysis_id);
}

#[cfg(test)]
mod tests {
    use super::*;
    // use tokio::sync::broadcast; // TODO: Implement broadcast for real-time updates
    
    #[tokio::test]
    async fn test_progress_update_serialization() {
        let update = ProgressUpdate {
            analysis_id: "test-123".to_string(),
            progress: 50.0,
            stage: "Parsing files".to_string(),
            message: Some("Processing TypeScript files".to_string()),
            timestamp: Utc::now(),
            files_processed: Some(500),
            total_files: Some(1000),
        };
        
        let json = match serde_json::to_string(&update) {
            Ok(json) => json,
            Err(e) => {
                tracing::error!("Failed to serialize progress update: {}", e);
                return;
            }
        };
        assert!(json.contains("\"analysis_id\":\"test-123\""));
        assert!(json.contains("\"progress\":50.0"));
        assert!(json.contains("\"stage\":\"Parsing files\""));
    }
}