//! Production-ready rate limiting using Tower Service Layer pattern
//! This works with the Tower-based auth middleware

use axum::{
    extract::{Request, State},
    http::StatusCode,
    middleware::Next,
    response::{IntoResponse, Response},
    J<PERSON>,
};
use crate::api::{AppState, auth_extractor::{AuthUser, AuthRequestExt}, errors::{ErrorResponse, ErrorType}};
use crate::errors::AnalysisError;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use std::sync::Arc;
use dashmap::DashMap;
use uuid::Uuid;

/// Rate limit information for the authenticated user
#[derive(Debug, Clone)]
pub struct RateLimitedUser {
    pub auth_user: AuthUser,
    pub remaining: i64,
    pub reset_time: u64,
}

lazy_static::lazy_static! {
    /// In-memory rate limit store
    static ref RATE_LIMIT_CACHE: DashMap<String, RateLimitEntry> = DashMap::new();
}

#[derive(Debug, <PERSON>lone)]
struct RateLimitEntry {
    count: i64,
    window_start: SystemTime,
}

/// Production rate limiting middleware using Tower pattern
pub async fn rate_limit_middleware(
    State(state): State<Arc<AppState>>,
    req: Request,
    next: Next,
) -> Response {
    // Extract authenticated user from request extensions
    let auth_user = match req.auth_user() {
        Ok(user) => user.clone(),
        Err(_) => {
            // No authenticated user - this middleware requires auth
            let error = ErrorResponse::new(
                ErrorType::Authentication,
                "Authentication required for rate limiting".to_string(),
            );
            return (StatusCode::UNAUTHORIZED, Json(error)).into_response();
        }
    };
    
    // Check rate limit
    let result = check_rate_limit(
        &auth_user.user_id,
        auth_user.rate_limit,
        &state
    ).await;
    
    let (allowed, remaining, reset_time) = match result {
        Ok(values) => values,
        Err(e) => {
            let error = ErrorResponse::new(
                ErrorType::Internal,
                format!("Rate limit check failed: {}", e),
            );
            return (StatusCode::INTERNAL_SERVER_ERROR, Json(error)).into_response();
        }
    };
    
    if !allowed {
        let mut error_response = ErrorResponse::new(
            ErrorType::RateLimit,
            "Rate limit exceeded".to_string()
        );
        error_response.error_code = Some("RATE_LIMIT_EXCEEDED".to_string());
        error_response.user_message = Some(format!(
            "You have exceeded your rate limit of {} requests. Please try again after {}.",
            auth_user.rate_limit,
            format_reset_time(reset_time)
        ));
        error_response.correlation_id = Some(Uuid::new_v4().to_string());
        error_response.retryable = true;
        error_response.retry_after_seconds = Some(reset_time as u32);
        
        let mut response = (StatusCode::TOO_MANY_REQUESTS, Json(error_response)).into_response();
        let headers = response.headers_mut();
        
        // Add rate limit headers
        if let Ok(limit_header) = auth_user.rate_limit.to_string().parse() {
            headers.insert("X-RateLimit-Limit", limit_header);
        }
        if let Ok(remaining_header) = remaining.to_string().parse() {
            headers.insert("X-RateLimit-Remaining", remaining_header);
        }
        if let Ok(reset_header) = reset_time.to_string().parse() {
            headers.insert("X-RateLimit-Reset", reset_header);
        }
        if let Ok(retry_header) = reset_time.to_string().parse() {
            headers.insert("Retry-After", retry_header);
        }
        
        return response;
    }
    
    // Add rate limit info to response headers
    let mut response = next.run(req).await;
    let headers = response.headers_mut();
    
    if let Ok(limit_header) = auth_user.rate_limit.to_string().parse() {
        headers.insert("X-RateLimit-Limit", limit_header);
    }
    if let Ok(remaining_header) = remaining.to_string().parse() {
        headers.insert("X-RateLimit-Remaining", remaining_header);
    }
    if let Ok(reset_header) = reset_time.to_string().parse() {
        headers.insert("X-RateLimit-Reset", reset_header);
    }
    
    response
}

/// Helper function to get rate limit info for a user without enforcing it
pub async fn get_rate_limit_info(
    user_id: &str,
    limit: i64,
    state: &Arc<AppState>,
) -> Result<(i64, u64), AnalysisError> {
    let (_, remaining, reset_time) = check_rate_limit(user_id, limit, state).await?;
    Ok((remaining, reset_time))
}

async fn check_rate_limit(
    user_id: &str,
    limit: i64,
    state: &Arc<AppState>,
) -> Result<(bool, i64, u64), AnalysisError> {
    // Try Redis first if available
    if let Some(pool) = &state.redis_pool {
        let redis_client = pool.get().await.map_err(|e| AnalysisError::internal(e.to_string()))?;
        match check_redis_rate_limit(user_id, limit, redis_client).await {
            Ok(result) => return Ok(result),
            Err(e) => {
                tracing::warn!("Redis rate limit check failed, falling back to memory: {}", e);
            }
        }
    }
    
    // Fallback to in-memory rate limiting
    check_memory_rate_limit(user_id, limit).await
}

async fn check_redis_rate_limit(
    user_id: &str,
    limit: i64,
    redis_client: &crate::storage::redis_client::RedisClient,
) -> Result<(bool, i64, u64), String> {
    let key = format!("rate_limit:{}", user_id);
    let window = 3600; // 1 hour window in seconds
    
    // Use the existing check_rate_limit method
    let (allowed, remaining, reset_seconds) = redis_client
        .check_rate_limit(&key, limit, window)
        .await
        .map_err(|e| format!("Redis error: {}", e))?;
    
    // Convert reset seconds to timestamp
    let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
    let reset_time = now + reset_seconds as u64;
    
    Ok((allowed, remaining as i64, reset_time))
}

async fn check_memory_rate_limit(
    user_id: &str,
    limit: i64,
) -> Result<(bool, i64, u64), AnalysisError> {
    let window = Duration::from_secs(3600); // 1 hour window
    let now = SystemTime::now();
    
    let mut entry = RATE_LIMIT_CACHE.entry(user_id.to_string()).or_insert_with(|| {
        RateLimitEntry {
            count: 0,
            window_start: now,
        }
    });
    
    // Check if we need to reset the window
    if now.duration_since(entry.window_start).unwrap_or(Duration::ZERO) >= window {
        entry.count = 0;
        entry.window_start = now;
    }
    
    // Increment the count
    entry.count += 1;
    
    let count = entry.count;
    let window_start = entry.window_start;
    drop(entry); // Release the lock
    
    let remaining = (limit - count).max(0);
    let reset_time = window_start
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs() + window.as_secs();
    
    Ok((count <= limit, remaining, reset_time))
}

fn format_reset_time(reset_time: u64) -> String {
    let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs();
    let seconds_until_reset = reset_time.saturating_sub(now);
    
    if seconds_until_reset < 60 {
        format!("{} seconds", seconds_until_reset)
    } else if seconds_until_reset < 3600 {
        format!("{} minutes", seconds_until_reset / 60)
    } else {
        format!("{} hours", seconds_until_reset / 3600)
    }
}

// Periodic cleanup of old entries
pub async fn cleanup_rate_limit_cache() {
    let window = Duration::from_secs(3600);
    let now = SystemTime::now();
    
    RATE_LIMIT_CACHE.retain(|_, entry| {
        now.duration_since(entry.window_start).unwrap_or(Duration::ZERO) < window * 2
    });
}