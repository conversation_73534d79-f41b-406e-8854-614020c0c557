use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use validator::Validate;

/// Security vulnerability detection result
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SecurityVulnerability {
    pub vulnerability_id: String,
    pub analysis_id: String,
    pub cve_id: Option<String>,
    pub cwe_id: Option<String>,
    pub vulnerability_type: VulnerabilityType,
    pub severity: SecuritySeverity,
    pub confidence_score: f64, // 0.0 to 1.0
    pub file_path: String,
    pub line_start: Option<i64>,
    pub line_end: Option<i64>,
    pub code_snippet: Option<String>,
    pub description: String,
    pub remediation_advice: Option<String>,
    pub owasp_category: Option<String>,
    pub attack_vector: Option<String>,
    pub exploitability_score: Option<f64>,
    pub impact_score: Option<f64>,
    pub false_positive_probability: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// Types of security vulnerabilities
#[derive(Debug, <PERSON>lone, Deserialize, Serialize)]
pub enum VulnerabilityType {
    SqlInjection,
    CrossSiteScripting,
    CrossSiteRequestForgery,
    InsecureDeserialization,
    BrokenAuthentication,
    SensitiveDataExposure,
    XmlExternalEntities,
    BrokenAccessControl,
    SecurityMisconfiguration,
    InsufficientLogging,
    CodeInjection,
    CommandInjection,
    PathTraversal,
    BufferOverflow,
    IntegerOverflow,
    UseAfterFree,
    RaceCondition,
    WeakCryptography,
    HardcodedCredentials,
    InsufficientInputValidation,
    ImproperErrorHandling,
    InsecureRandomness,
    TimeOfCheckTimeOfUse,
    UncontrolledResourceConsumption,
    HardcodedSecret,
    NoSqlInjection,
    XmlExternalEntity,
    TemplateInjection,
    OpenRedirect,
    LdapInjection,
    InsecureCookie,
    HttpResponseSplitting,
    Other(String),
}

impl VulnerabilityType {
    pub fn as_str(&self) -> &str {
        match self {
            VulnerabilityType::SqlInjection => "sql_injection",
            VulnerabilityType::CrossSiteScripting => "cross_site_scripting",
            VulnerabilityType::CrossSiteRequestForgery => "cross_site_request_forgery",
            VulnerabilityType::InsecureDeserialization => "insecure_deserialization",
            VulnerabilityType::BrokenAuthentication => "broken_authentication",
            VulnerabilityType::SensitiveDataExposure => "sensitive_data_exposure",
            VulnerabilityType::XmlExternalEntities => "xml_external_entities",
            VulnerabilityType::BrokenAccessControl => "broken_access_control",
            VulnerabilityType::SecurityMisconfiguration => "security_misconfiguration",
            VulnerabilityType::InsufficientLogging => "insufficient_logging",
            VulnerabilityType::CodeInjection => "code_injection",
            VulnerabilityType::CommandInjection => "command_injection",
            VulnerabilityType::PathTraversal => "path_traversal",
            VulnerabilityType::BufferOverflow => "buffer_overflow",
            VulnerabilityType::IntegerOverflow => "integer_overflow",
            VulnerabilityType::UseAfterFree => "use_after_free",
            VulnerabilityType::RaceCondition => "race_condition",
            VulnerabilityType::WeakCryptography => "weak_cryptography",
            VulnerabilityType::HardcodedCredentials => "hardcoded_credentials",
            VulnerabilityType::InsufficientInputValidation => "insufficient_input_validation",
            VulnerabilityType::ImproperErrorHandling => "improper_error_handling",
            VulnerabilityType::InsecureRandomness => "insecure_randomness",
            VulnerabilityType::TimeOfCheckTimeOfUse => "time_of_check_time_of_use",
            VulnerabilityType::UncontrolledResourceConsumption => {
                "uncontrolled_resource_consumption"
            }
            VulnerabilityType::HardcodedSecret => "hardcoded_secret",
            VulnerabilityType::NoSqlInjection => "nosql_injection",
            VulnerabilityType::XmlExternalEntity => "xml_external_entity",
            VulnerabilityType::TemplateInjection => "template_injection",
            VulnerabilityType::OpenRedirect => "open_redirect",
            VulnerabilityType::LdapInjection => "ldap_injection",
            VulnerabilityType::InsecureCookie => "insecure_cookie",
            VulnerabilityType::HttpResponseSplitting => "http_response_splitting",
            VulnerabilityType::Other(name) => name,
        }
    }
}

/// Dependency vulnerability information
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DependencyVulnerability {
    pub dependency_vuln_id: String,
    pub analysis_id: String,
    pub dependency_name: String,
    pub dependency_version: String,
    pub package_manager: PackageManager,
    pub cve_id: Option<String>,
    pub vulnerability_source: VulnerabilitySource,
    pub severity: SecuritySeverity,
    pub cvss_score: Option<f64>,
    pub cvss_vector: Option<String>,
    pub description: Option<String>,
    pub published_date: Option<DateTime<Utc>>,
    pub last_modified_date: Option<DateTime<Utc>>,
    pub affected_versions: Vec<String>,
    pub patched_versions: Vec<String>,
    pub workaround: Option<String>,
    pub exploit_available: bool,
    pub proof_of_concept_available: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Deserialize, Serialize, PartialEq)]
pub enum PackageManager {
    Npm,
    Pip,
    Cargo,
    Composer,
    Maven,
    Gradle,
    Nuget,
    Go,
    Ruby,
    NuGet,
    Gem,
    Other(String),
}

impl PackageManager {
    pub fn as_str(&self) -> &str {
        match self {
            PackageManager::Npm => "npm",
            PackageManager::Pip => "pip",
            PackageManager::Cargo => "cargo",
            PackageManager::Composer => "composer",
            PackageManager::Maven => "maven",
            PackageManager::Gradle => "gradle",
            PackageManager::Nuget => "nuget",
            PackageManager::Go => "go",
            PackageManager::Ruby => "ruby",
            PackageManager::NuGet => "nuget",
            PackageManager::Gem => "gem",
            PackageManager::Other(name) => name,
        }
    }
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum VulnerabilitySource {
    SAST,
    NationalVulnerabilityDatabase,
    GitHub,
    Snyk,
    WhiteSource,
    Sonatype,
    RustSec,
    PyUp,
    NodeSecurity,
    Other(String),
}

impl VulnerabilitySource {
    pub fn as_str(&self) -> &str {
        match self {
            VulnerabilitySource::NationalVulnerabilityDatabase => "nvd",
            VulnerabilitySource::GitHub => "github",
            VulnerabilitySource::Snyk => "snyk",
            VulnerabilitySource::WhiteSource => "whitesource",
            VulnerabilitySource::Sonatype => "sonatype",
            VulnerabilitySource::RustSec => "rustsec",
            VulnerabilitySource::PyUp => "pyup",
            VulnerabilitySource::NodeSecurity => "nodesecurity",
            VulnerabilitySource::Other(name) => name,
        }
    }
}

/// Detected secret in source code
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct DetectedSecret {
    pub secret_id: String,
    pub analysis_id: String,
    pub secret_type: SecretType,
    pub file_path: String,
    pub line_number: Option<i64>,
    pub secret_hash: Option<String>, // SHA-256 hash for deduplication
    pub entropy_score: Option<f64>,
    pub pattern_name: String,
    pub confidence_score: f64, // 0.0 to 1.0
    pub is_false_positive: bool,
    pub is_test_data: bool,
    pub severity: SecuritySeverity,
    pub context: Option<String>,      // Surrounding code context
    pub masked_value: Option<String>, // Partially masked secret
    pub created_at: DateTime<Utc>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum SecretType {
    ApiKey,
    Password,
    Token,
    Certificate,
    PrivateKey,
    DatabaseUrl,
    JwtSecret,
    EncryptionKey,
    AwsAccessKey,
    GcpServiceAccount,
    AzureKey,
    StripeKey,
    SlackToken,
    GitHubToken,
    TwilioApiKey,
    StripeApiKey,
    SshPrivateKey,
    SlackWebhook,
    SendgridApiKey,
    PypiToken,
    PgpPrivateKey,
    OauthClientSecret,
    NpmToken,
    MailgunApiKey,
    Jwt,
    HerokuApiKey,
    GoogleApiKey,
    GitlabToken,
    GithubToken,
    DigitalOceanToken,
    DatabaseConnectionString,
    AzureClientSecret,
    AwsSecretKey,
    Other(String),
}

impl SecretType {
    pub fn as_str(&self) -> &str {
        match self {
            SecretType::ApiKey => "api_key",
            SecretType::Password => "password",
            SecretType::Token => "token",
            SecretType::Certificate => "certificate",
            SecretType::PrivateKey => "private_key",
            SecretType::DatabaseUrl => "database_url",
            SecretType::JwtSecret => "jwt_secret",
            SecretType::EncryptionKey => "encryption_key",
            SecretType::AwsAccessKey => "aws_access_key",
            SecretType::GcpServiceAccount => "gcp_service_account",
            SecretType::AzureKey => "azure_key",
            SecretType::StripeKey => "stripe_key",
            SecretType::SlackToken => "slack_token",
            SecretType::GitHubToken => "github_token",
            SecretType::TwilioApiKey => "twilio_api_key",
            SecretType::StripeApiKey => "stripe_api_key",
            SecretType::SshPrivateKey => "ssh_private_key",
            SecretType::SlackWebhook => "slack_webhook",
            SecretType::SendgridApiKey => "sendgrid_api_key",
            SecretType::PypiToken => "pypi_token",
            SecretType::PgpPrivateKey => "pgp_private_key",
            SecretType::OauthClientSecret => "oauth_client_secret",
            SecretType::NpmToken => "npm_token",
            SecretType::MailgunApiKey => "mailgun_api_key",
            SecretType::Jwt => "jwt",
            SecretType::HerokuApiKey => "heroku_api_key",
            SecretType::GoogleApiKey => "google_api_key",
            SecretType::GitlabToken => "gitlab_token",
            SecretType::GithubToken => "github_token",
            SecretType::DigitalOceanToken => "digital_ocean_token",
            SecretType::DatabaseConnectionString => "database_connection_string",
            SecretType::AzureClientSecret => "azure_client_secret",
            SecretType::AwsSecretKey => "aws_secret_key",
            SecretType::Other(name) => name,
        }
    }
}

/// Compliance violation
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ComplianceViolation {
    pub violation_id: String,
    pub analysis_id: String,
    pub compliance_framework: ComplianceFramework,
    pub rule_id: String,
    pub rule_name: String,
    pub violation_type: String,
    pub severity: SecuritySeverity,
    pub file_path: Option<String>,
    pub line_number: Option<i64>,
    pub description: String,
    pub remediation_guidance: Option<String>,
    pub compliance_category: Option<String>,
    pub risk_rating: RiskRating,
    pub business_impact: Option<String>,
    pub technical_debt_hours: Option<f64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Deserialize, Serialize, Hash, Eq, PartialEq)]
pub enum ComplianceFramework {
    OWASP,
    CWE,
    NIST,
    SOC2,
    HIPAA,
    GDPR,
    PciDss,
    ISO27001,
    Soc2,
    Owasp,
    Nist,
    Iso27001,
    Hipaa,
    Gdpr,
    Cis,
    Other(String),
}

impl ComplianceFramework {
    pub fn as_str(&self) -> &str {
        match self {
            ComplianceFramework::OWASP => "OWASP",
            ComplianceFramework::CWE => "CWE",
            ComplianceFramework::NIST => "NIST",
            ComplianceFramework::SOC2 => "SOC2",
            ComplianceFramework::HIPAA => "HIPAA",
            ComplianceFramework::GDPR => "GDPR",
            ComplianceFramework::PciDss => "PCI_DSS",
            ComplianceFramework::ISO27001 => "ISO27001",
            ComplianceFramework::Soc2 => "SOC2",
            ComplianceFramework::Owasp => "OWASP",
            ComplianceFramework::Nist => "NIST",
            ComplianceFramework::Iso27001 => "ISO27001",
            ComplianceFramework::Hipaa => "HIPAA",
            ComplianceFramework::Gdpr => "GDPR",
            ComplianceFramework::Cis => "CIS",
            ComplianceFramework::Other(name) => name,
        }
    }
}

/// Security assessment and scoring
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SecurityAssessment {
    pub assessment_id: String,
    pub analysis_id: String,
    pub overall_security_score: f64, // 0.0 to 100.0
    pub vulnerability_score: f64,
    pub dependency_score: f64,
    pub secrets_score: f64,
    pub compliance_score: f64,
    pub risk_level: RiskLevel,
    pub total_vulnerabilities: i64,
    pub critical_vulnerabilities: i64,
    pub high_vulnerabilities: i64,
    pub medium_vulnerabilities: i64,
    pub low_vulnerabilities: i64,
    pub total_secrets_found: i64,
    pub high_entropy_secrets: i64,
    pub compliance_violations_count: i64,
    pub security_debt_score: Option<f64>,
    pub improvement_recommendations: Vec<String>,
    pub recommendations: Vec<String>,
    pub detailed_findings: Vec<String>,
    pub risk_matrix: Option<String>,
    pub trending_direction: TrendingDirection,
    pub created_at: DateTime<Utc>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// Threat model
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct ThreatModel {
    pub threat_model_id: String,
    pub analysis_id: String,
    pub threat_category: ThreatCategory,
    pub threat_name: String,
    pub threat_description: String,
    pub threat_actor: Option<ThreatActor>,
    pub attack_vector: Option<String>,
    pub asset_affected: Option<String>,
    pub likelihood: Likelihood,
    pub impact: Impact,
    pub risk_score: f64,
    pub mitigation_status: MitigationStatus,
    pub mitigation_measures: Vec<String>,
    pub mitigation_strategy: Option<String>,
    pub exploit_likelihood: Option<f64>,
    pub business_impact: Option<String>,
    pub residual_risk_score: Option<f64>,
    pub associated_vulnerabilities: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum ThreatCategory {
    Spoofing,
    Tampering,
    Repudiation,
    InformationDisclosure,
    DenialOfService,
    ElevationOfPrivilege,
    SecurityMisconfiguration,
    RemoteCodeExecution,
    DataBreach,
    UnauthorizedAccess,
    SupplyChainAttack,
    ApplicationVulnerability,
    Other(String),
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum ThreatActor {
    Insider,
    External,
    NationState,
    Cybercriminal,
    Hacktivist,
    ScriptKiddie,
    Other(String),
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum MitigationStatus {
    NotMitigated,
    PartiallyMitigated,
    FullyMitigated,
}

/// Security intelligence metadata
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SecurityIntelligenceMetadata {
    pub metadata_id: String,
    pub analysis_id: String,
    pub threat_intel_sources: Vec<String>,
    pub last_threat_intel_update: Option<DateTime<Utc>>,
    pub vulnerability_databases_used: Vec<String>,
    pub ml_models_used: Vec<String>,
    pub detection_rules_version: Option<String>,
    pub false_positive_rate: Option<f64>,
    pub detection_accuracy: Option<f64>,
    pub scan_duration_ms: Option<i64>,
    pub total_files_scanned: Option<i64>,
    pub total_dependencies_scanned: Option<i64>,
    pub created_at: DateTime<Utc>,
    pub updated_at: Option<DateTime<Utc>>,
}

/// Common enums and types

#[derive(Debug, Clone, Copy, Deserialize, Serialize, PartialEq, Eq, PartialOrd, Ord, Hash)]
pub enum SecuritySeverity {
    Info,
    Low,
    Medium,
    High,
    Critical,
}

impl SecuritySeverity {
    pub fn as_str(&self) -> &str {
        match self {
            SecuritySeverity::Info => "info",
            SecuritySeverity::Low => "low",
            SecuritySeverity::Medium => "medium",
            SecuritySeverity::High => "high",
            SecuritySeverity::Critical => "critical",
        }
    }

    pub fn score(&self) -> f64 {
        match self {
            SecuritySeverity::Info => 0.0,
            SecuritySeverity::Low => 25.0,
            SecuritySeverity::Medium => 50.0,
            SecuritySeverity::High => 75.0,
            SecuritySeverity::Critical => 100.0,
        }
    }
}

impl std::fmt::Display for SecuritySeverity {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.as_str())
    }
}

#[derive(Debug, Clone, Deserialize, Serialize, Debug, Clone, Deserialize, Serialize, PartialEq)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

impl RiskLevel {
    pub fn as_str(&self) -> &str {
        match self {
            RiskLevel::Low => "low",
            RiskLevel::Medium => "medium",
            RiskLevel::High => "high",
            RiskLevel::Critical => "critical",
        }
    }
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum RiskRating {
    Low,
    Medium,
    High,
    Critical,
}

impl RiskRating {
    pub fn as_str(&self) -> &str {
        match self {
            RiskRating::Low => "low",
            RiskRating::Medium => "medium",
            RiskRating::High => "high",
            RiskRating::Critical => "critical",
        }
    }
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum Likelihood {
    VeryLow,
    Low,
    Medium,
    High,
    VeryHigh,
}

impl Likelihood {
    pub fn as_str(&self) -> &str {
        match self {
            Likelihood::VeryLow => "very_low",
            Likelihood::Low => "low",
            Likelihood::Medium => "medium",
            Likelihood::High => "high",
            Likelihood::VeryHigh => "very_high",
        }
    }

    pub fn score(&self) -> f64 {
        match self {
            Likelihood::VeryLow => 1.0,
            Likelihood::Low => 2.0,
            Likelihood::Medium => 3.0,
            Likelihood::High => 4.0,
            Likelihood::VeryHigh => 5.0,
        }
    }
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum Impact {
    VeryLow,
    Low,
    Medium,
    High,
    VeryHigh,
    Critical,
    Minimal,
}

impl Impact {
    pub fn as_str(&self) -> &str {
        match self {
            Impact::VeryLow => "very_low",
            Impact::Low => "low",
            Impact::Medium => "medium",
            Impact::High => "high",
            Impact::VeryHigh => "very_high",
            Impact::Critical => "critical",
            Impact::Minimal => "minimal",
        }
    }

    pub fn score(&self) -> f64 {
        match self {
            Impact::VeryLow => 1.0,
            Impact::Low => 2.0,
            Impact::Medium => 3.0,
            Impact::High => 4.0,
            Impact::VeryHigh => 5.0,
            Impact::Critical => 6.0,
            Impact::Minimal => 0.5,
        }
    }
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum TrendingDirection {
    Improving,
    Declining,
    Stable,
}

impl TrendingDirection {
    pub fn as_str(&self) -> &str {
        match self {
            TrendingDirection::Improving => "improving",
            TrendingDirection::Declining => "declining",
            TrendingDirection::Stable => "stable",
        }
    }
}

/// Request/Response models for the security intelligence API

#[derive(
    Debug,
    Clone,
    Deserialize,
    Serialize,
    Validate,
    Debug,
    Clone,
    Serialize,
    Deserialize,
    Validate,
    Debug,
    Clone,
    Deserialize,
    Serialize,
    Validate,
)]
pub struct SecurityAnalysisRequest {
    #[validate(length(min = 1, max = 255))]
    pub analysis_id: String,
    pub enable_vulnerability_detection: bool,
    pub enable_dependency_scanning: bool,
    pub enable_secrets_detection: bool,
    pub enable_compliance_checking: bool,
    pub enable_threat_modeling: bool,
    pub threat_intel_enabled: bool,
    pub compliance_frameworks: Vec<ComplianceFramework>,
    pub scan_depth: SecurityScanDepth,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum SecurityScanDepth {
    Quick,    // Basic patterns only
    Standard, // Standard ML-enhanced detection
    Deep,     // Full ML analysis with threat intelligence
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SecurityAnalysisResult {
    pub analysis_id: String,
    pub vulnerabilities: Vec<SecurityVulnerability>,
    pub dependency_vulnerabilities: Vec<DependencyVulnerability>,
    pub detected_secrets: Vec<DetectedSecret>,
    pub compliance_violations: Vec<ComplianceViolation>,
    pub security_assessment: SecurityAssessment,
    pub threat_models: Vec<ThreatModel>,
    pub metadata: SecurityIntelligenceMetadata,
}

/// Security audit event
#[derive(Debug, Clone, Deserialize, Serialize)]
pub struct SecurityAuditEvent {
    pub log_id: String,
    pub analysis_id: Option<String>,
    pub event_type: SecurityEventType,
    pub event_severity: SecuritySeverity,
    pub event_details: HashMap<String, serde_json::Value>,
    pub user_id: Option<String>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Deserialize, Serialize)]
pub enum SecurityEventType {
    VulnerabilityDetected,
    SecretFound,
    ThreatIdentified,
    ComplianceViolationDetected,
    SecurityScanStarted,
    SecurityScanCompleted,
    ThreatIntelligenceUpdated,
    SecurityPolicyViolation,
    SuspiciousActivity,
    SecurityIncident,
}

impl SecurityEventType {
    pub fn as_str(&self) -> &str {
        match self {
            SecurityEventType::VulnerabilityDetected => "vulnerability_detected",
            SecurityEventType::SecretFound => "secret_found",
            SecurityEventType::ThreatIdentified => "threat_identified",
            SecurityEventType::ComplianceViolationDetected => "compliance_violation_detected",
            SecurityEventType::SecurityScanStarted => "security_scan_started",
            SecurityEventType::SecurityScanCompleted => "security_scan_completed",
            SecurityEventType::ThreatIntelligenceUpdated => "threat_intelligence_updated",
            SecurityEventType::SecurityPolicyViolation => "security_policy_violation",
            SecurityEventType::SuspiciousActivity => "suspicious_activity",
            SecurityEventType::SecurityIncident => "security_incident",
        }
    }
}
