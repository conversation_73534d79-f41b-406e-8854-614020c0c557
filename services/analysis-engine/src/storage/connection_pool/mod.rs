//! Connection pooling for Spanner and Redis
use async_trait::async_trait;
use bb8::{ManageConnection, Pool, PooledConnection};
use google_cloud_spanner::client::Client;
use redis::aio::MultiplexedConnection as Connection;

use crate::config::{GcpSettings, RedisSettings};
use crate::storage::gcp_clients;

/// Manages Spanner connections for the connection pool
pub struct SpannerConnectionManager {
    config: GcpSettings,
}

impl SpannerConnectionManager {
    pub fn new(config: GcpSettings) -> Self {
        Self { config }
    }
}

#[async_trait]
impl ManageConnection for SpannerConnectionManager {
    type Connection = Client;
    type Error = anyhow::Error;

    async fn connect(&self) -> Result<Self::Connection, Self::Error> {
        gcp_clients::create_spanner_client(&self.config).await
    }

    async fn is_valid(&self, conn: &mut Self::Connection) -> Result<(), Self::Error> {
        // Spanner client doesn't have a simple ping, so we'll assume it's valid
        // if we can get a session. This is a bit heavy, so we'll do it infrequently.
        let _ = conn.batch_read_only_transaction().await?;
        Ok(())
    }

    fn has_broken(&self, _conn: &mut Self::Connection) -> bool {
        false
    }
}

/// Manages Redis connections for the connection pool
pub struct RedisConnectionManager {
    config: RedisSettings,
}

impl RedisConnectionManager {
    pub fn new(config: RedisSettings) -> Self {
        Self { config }
    }
}

#[async_trait]
impl ManageConnection for RedisConnectionManager {
    type Connection = Connection;
    type Error = redis::RedisError;

    async fn connect(&self) -> Result<Self::Connection, Self::Error> {
        let client = redis::Client::open(self.config.url.as_str())?;
        client.get_multiplexed_async_connection().await
    }

    async fn is_valid(&self, conn: &mut Self::Connection) -> Result<(), Self::Error> {
        let mut conn = conn.clone();
        redis::cmd("PING").query_async(&mut conn).await?;
        Ok(())
    }

    fn has_broken(&self, _conn: &mut Self::Connection) -> bool {
        false
    }
}

pub type SpannerPool = Pool<SpannerConnectionManager>;
pub type RedisPool = Pool<RedisConnectionManager>;