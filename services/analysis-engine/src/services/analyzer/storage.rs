use crate::backpressure::BackpressureManager;
use crate::models::AnalysisResult;
use crate::storage::{SpannerOperations, StorageOperations, connection_pool::SpannerPool};
use anyhow::Result;
use std::sync::Arc;

pub struct StorageManager {
    spanner_pool: Option<Arc<SpannerPool>>,
    storage_client: Arc<StorageOperations>,
    backpressure_manager: Option<Arc<BackpressureManager>>,
}

impl StorageManager {
    pub fn new(
        spanner_pool: Option<Arc<SpannerPool>>,
        storage_client: Arc<StorageOperations>,
        backpressure_manager: Option<Arc<BackpressureManager>>,
    ) -> Self {
        Self {
            spanner_pool,
            storage_client,
            backpressure_manager,
        }
    }

    /// Store analysis result in both Spanner and cloud storage
    pub async fn store_analysis_result(&self, result: &AnalysisResult) -> Result<()> {
        // Store in Spanner if available
        if let Some(pool) = &self.spanner_pool {
           let spanner = pool.get().await?;
            self.store_in_spanner(spanner, result).await?;
        } else {
            tracing::warn!(
                "Spanner not available - skipping database storage for analysis {}",
                result.id
            );
        }

        // Always store in cloud storage as backup
        self.storage_client.store_analysis_results(result).await?;

        Ok(())
    }

    /// Store analysis result in Spanner with backpressure control
    async fn store_in_spanner(
        &self,
        spanner: bb8::PooledConnection<'_, crate::storage::connection_pool::SpannerConnectionManager<'_>>,
        result: &AnalysisResult,
    ) -> Result<()> {
        // Acquire database permit for backpressure control
        let _db_permit = if let Some(bp_manager) = &self.backpressure_manager {
            Some(bp_manager.acquire_database_permit().await?)
        } else {
            None
        };

        // Store main analysis metadata
        match spanner.store_analysis(result).await {
            Ok(_) => {
                if let Some(bp_manager) = &self.backpressure_manager {
                    bp_manager.record_success("database").await;
                }
                tracing::info!("Stored analysis {} in Spanner", result.id);
            }
            Err(e) => {
                if let Some(bp_manager) = &self.backpressure_manager {
                    bp_manager.record_failure("database").await;
                }
                return Err(e);
            }
        }

        // Store file-level analysis data if available
        if let Some(file_analyses) = &result.successful_analyses {
            self.store_file_analyses(spanner, &result.id, file_analyses)
                .await?;
        }

        // Store pattern details if available
        if !result.patterns.is_empty() {
            self.store_pattern_details(spanner, &result.id, &result.patterns)
                .await?;
        }

        Ok(())
    }

    /// Store individual file analyses
    async fn store_file_analyses(
        &self,
        spanner: &Arc<SpannerOperations>,
        analysis_id: &str,
        file_analyses: &[crate::models::FileAnalysis],
    ) -> Result<()> {
        let mut stored_count = 0;
        let mut failed_count = 0;

        for file_analysis in file_analyses {
            match spanner.store_file_analysis(analysis_id, file_analysis).await {
                Ok(_) => {
                    stored_count += 1;
                }
                Err(e) => {
                    tracing::warn!(
                        "Failed to store file analysis for {}: {}",
                        file_analysis.path,
                        e
                    );
                    failed_count += 1;
                }
            }

            // Yield periodically to avoid blocking
            if (stored_count + failed_count) % 100 == 0 {
                tokio::task::yield_now().await;
            }
        }

        tracing::info!(
            "Stored {} file analyses for analysis {} ({} failed)",
            stored_count,
            analysis_id,
            failed_count
        );

        if failed_count > 0 && stored_count == 0 {
            return Err(anyhow::anyhow!(
                "Failed to store any file analyses for analysis {}",
                analysis_id
            ));
        }

        Ok(())
    }

    /// Store pattern details
    async fn store_pattern_details(
        &self,
        spanner: &Arc<SpannerOperations>,
        analysis_id: &str,
        patterns: &[crate::models::DetectedPattern],
    ) -> Result<()> {
        match spanner.store_pattern_details(analysis_id, patterns).await {
            Ok(_) => {
                tracing::info!(
                    "Stored {} patterns for analysis {}",
                    patterns.len(),
                    analysis_id
                );
                Ok(())
            }
            Err(e) => {
                tracing::warn!(
                    "Failed to store pattern details for analysis {}: {}",
                    analysis_id,
                    e
                );
                // Don't fail the entire operation if pattern storage fails
                Ok(())
            }
        }
    }

    /// Store embeddings in cloud storage
    pub async fn store_embeddings(
        &self,
        analysis_id: &str,
        embeddings: &[crate::models::CodeEmbedding],
    ) -> Result<()> {
        // Embeddings are typically large, so we store them in cloud storage
        let embeddings_path = format!("embeddings/{}.json", analysis_id);
        let embeddings_json = serde_json::to_string(embeddings)?;

        self.storage_client
            .store_raw_data(&embeddings_path, embeddings_json.as_bytes())
            .await?;

        tracing::info!(
            "Stored {} embeddings for analysis {} in cloud storage",
            embeddings.len(),
            analysis_id
        );

        Ok(())
    }

    /// Retrieve analysis result from storage
    pub async fn get_analysis_result(&self, analysis_id: &str) -> Result<AnalysisResult> {
        // Try Spanner first if available
        if let Some(pool) = &self.spanner_pool {
            let spanner = pool.get().await?;
            match spanner.get_analysis(analysis_id).await {
                Ok(Some(result)) => return Ok(result),
                Ok(None) => {
                    tracing::warn!("Analysis {} not found in cache", analysis_id);
                }
                Err(e) => {
                    tracing::warn!(
                        "Failed to retrieve analysis {} from Spanner: {}",
                        analysis_id,
                        e
                    );
                }
            }
        }

        // Fall back to cloud storage
        match self.storage_client.get_analysis_results(analysis_id).await? {
            Some(result) => Ok(result),
            None => Err(anyhow::anyhow!("Analysis {} not found", analysis_id)),
        }
    }

    /// Delete analysis result from storage
    pub async fn delete_analysis_result(&self, analysis_id: &str) -> Result<()> {
        let mut errors = Vec::new();

        // Delete from Spanner if available
        if let Some(pool) = &self.spanner_pool {
            let spanner = pool.get().await?;
            if let Err(e) = spanner.delete_analysis(analysis_id).await {
                errors.push(format!("Spanner deletion failed: {}", e));
            }
        }

        // Delete from cloud storage
        if let Err(e) = self.storage_client.delete_analysis_results(analysis_id).await {
            errors.push(format!("Cloud storage deletion failed: {}", e));
        }

        if !errors.is_empty() {
            return Err(anyhow::anyhow!(
                "Failed to delete analysis {}: {}",
                analysis_id,
                errors.join(", ")
            ));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::{AnalysisStatus, FileAnalysis};
    use chrono::Utc;

    fn create_test_analysis_result() -> AnalysisResult {
        AnalysisResult {
            id: "test-analysis-id".to_string(),
            repository_url: "https://github.com/test/repo".to_string(),
            branch: "main".to_string(),
            commit_hash: Some("abc123".to_string()),
            repository_size_bytes: Some(1024 * 1024),
            clone_time_ms: Some(1000),
            status: AnalysisStatus::Completed,
            started_at: Utc::now(),
            completed_at: Some(Utc::now()),
            duration_seconds: Some(60),
            file_count: 10,
            success_rate: 90.0,
            progress: Some(100.0),
            current_stage: Some("Completed".to_string()),
            estimated_completion: None,
            patterns: vec![],
            languages: std::collections::HashMap::new(),
            embeddings: None,
            successful_analyses: Some(vec![]),
            user_id: "test-user".to_string(),
            webhook_url: None,
            failed_files: vec![],
            metrics: None,
            performance_metrics: None,
            error_message: None,
            warnings: vec![],
        }
    }

    #[tokio::test]
    async fn test_store_analysis_result_without_spanner() {
        // Create storage manager without Spanner
        let storage_client = Arc::new(StorageOperations::new("test-bucket".to_string()));
        let storage_manager = StorageManager::new(None, storage_client, None);

        let result = create_test_analysis_result();

        // This should succeed even without Spanner (storing only in cloud storage)
        // Note: This will fail in tests without actual GCS setup, but demonstrates the API
        let store_result = storage_manager.store_analysis_result(&result).await;
        
        // In a real test environment with mocked storage, this would succeed
        assert!(store_result.is_err()); // Expected in test environment without GCS
    }

    #[test]
    fn test_storage_manager_creation() {
        let storage_client = Arc::new(StorageOperations::new("test-bucket".to_string()));
        let storage_manager = StorageManager::new(None, storage_client.clone(), None);

        assert!(storage_manager.spanner_pool.is_none());
        assert!(storage_manager.backpressure_manager.is_none());
    }
}