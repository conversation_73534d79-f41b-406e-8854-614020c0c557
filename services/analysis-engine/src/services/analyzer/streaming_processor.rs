use crate::backpressure::BackpressureManager;
use crate::models::*;
use crate::parser::{TreeSitterParser, StreamingAnalysisConfig};
use crate::parser::streaming::DefaultProgressReporter;
use anyhow::{Context, Result};
use chrono::Utc;
use std::path::PathBuf;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::sync::Arc;
use tokio::sync::{mpsc, Semaphore};

pub struct StreamingProcessor {
    parser: Arc<TreeSitterParser>,
    backpressure_manager: Option<Arc<BackpressureManager>>,
}

impl StreamingProcessor {
    pub fn new(
        parser: Arc<TreeSitterParser>,
        backpressure_manager: Option<Arc<BackpressureManager>>,
    ) -> Self {
        Self {
            parser,
            backpressure_manager,
        }
    }

    /// Parse files using streaming approach for large repositories
    pub async fn parse_files_with_streaming(
        &self,
        files: &[PathBuf],
        progress_tx: mpsc::Sender<ProgressUpdate>,
        analysis_id: String,
    ) -> Result<Vec<Result<FileAnalysis, ParseError>>> {
        tracing::info!("Using streaming processor for large repository analysis");

        let streaming_config = StreamingAnalysisConfig {
            max_file_size: 3 * 1024 * 1024 * 1024, // 3GB max file size
            file_timeout: std::time::Duration::from_secs(300), // 5 minutes timeout
            collect_metrics: true,
            extract_symbols: true,
            extract_chunks: true,
            max_ast_depth: 100,
        };
        
        // Configuration for streaming processing
        let concurrent_file_processors = num_cpus::get().min(8);
        let file_threshold = 10 * 1024 * 1024; // 10MB
        let _read_buffer_size = 64 * 1024;

        let _progress_reporter = Arc::new(DefaultProgressReporter::new());

        // StreamingFileProcessor will be initialized when needed
        // as it requires StreamingConfig, not StreamingAnalysisConfig

        // Enhanced streaming processing with backpressure management
        let mut results = Vec::with_capacity(files.len());
        let total_files = files.len();
        let processed_count = Arc::new(AtomicUsize::new(0));
        let error_count = Arc::new(AtomicUsize::new(0));

        // Create semaphore for concurrency control
        let semaphore = Arc::new(Semaphore::new(concurrent_file_processors));

        // Process files in adaptive batches based on memory pressure
        let initial_batch_size = (total_files / concurrent_file_processors)
            .max(1)
            .min(50);
        let mut batch_size = initial_batch_size;
        let mut handles = Vec::new();

        for (_batch_idx, batch) in files.chunks(batch_size).enumerate() {
            let batch_files = batch.to_vec();
            let parser_clone = self.parser.clone();
            let semaphore_clone = semaphore.clone();
            let processed_count_clone = processed_count.clone();
            let error_count_clone = error_count.clone();
            let config_clone = streaming_config.clone();
            let progress_tx_clone = progress_tx.clone();
            let analysis_id_clone = analysis_id.clone();
            let backpressure_manager = self.backpressure_manager.clone();

            let handle = tokio::spawn(async move {
                let mut batch_results = Vec::new();

                for file_path in batch_files {
                    // Acquire semaphore permit for concurrency control
                    let _permit = semaphore_clone.acquire().await.unwrap();

                    // Check backpressure before processing
                    if let Some(bp_manager) = &backpressure_manager {
                        if let Err(e) = bp_manager.check_memory_pressure().await {
                            tracing::warn!("Memory pressure detected, throttling: {}", e);
                            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
                        }
                    }

                    // Get file size for processing decision
                    let file_size = tokio::fs::metadata(&file_path)
                        .await
                        .map(|m| m.len())
                        .unwrap_or(0);

                    // Choose processing method based on file size
                    let result = if file_size > file_threshold {
                        Self::process_large_file_with_streaming(
                            &parser_clone,
                            &file_path,
                            &config_clone,
                        )
                        .await
                    } else {
                        parser_clone.parse_file(&file_path).await
                    };

                    // Update counters
                    let processed = processed_count_clone.fetch_add(1, Ordering::Relaxed) + 1;

                    if result.is_err() {
                        error_count_clone.fetch_add(1, Ordering::Relaxed);
                    }

                    // Report progress
                    if processed % 10 == 0 || processed == total_files {
                        let progress = 35.0 + (processed as f64 / total_files as f64) * 35.0;
                        let errors = error_count_clone.load(Ordering::Relaxed);
                        let success_rate = ((processed - errors) as f64 / processed as f64) * 100.0;

                        let _ = progress_tx_clone
                            .send(ProgressUpdate {
                                analysis_id: analysis_id_clone.clone(),
                                progress,
                                stage: format!(
                                    "Streaming: {}/{} files ({}% success)",
                                    processed,
                                    total_files,
                                    success_rate as u32
                                ),
                                message: Some(format!("Large file processing: {} MB", file_size / 1024 / 1024)),
                                timestamp: Utc::now(),
                                files_processed: Some(processed),
                                total_files: Some(total_files),
                            })
                            .await;
                    }

                    batch_results.push(result);
                }

                batch_results
            });

            handles.push(handle);

            // Adaptive batch sizing based on error rate
            let current_errors = error_count.load(Ordering::Relaxed);
            let current_processed = processed_count.load(Ordering::Relaxed);

            if current_processed > 0 {
                let error_rate = current_errors as f64 / current_processed as f64;
                if error_rate > 0.1 {
                    // More than 10% errors
                    batch_size = (batch_size / 2).max(1); // Reduce batch size
                    tracing::warn!(
                        "High error rate detected ({:.1}%), reducing batch size to {}",
                        error_rate * 100.0,
                        batch_size
                    );
                } else if error_rate < 0.02 && batch_size < initial_batch_size * 2 {
                    // Less than 2% errors
                    batch_size = (batch_size + 5).min(initial_batch_size * 2); // Increase batch size
                }
            }
        }

        // Collect results from all batches
        for handle in handles {
            let batch_results = handle
                .await
                .context("Streaming batch processing failed")?;
            results.extend(batch_results);
        }

        let total_processed = processed_count.load(Ordering::Relaxed);
        let total_errors = error_count.load(Ordering::Relaxed);
        let success_rate = ((total_processed - total_errors) as f64 / total_processed as f64) * 100.0;

        tracing::info!(
            "Streaming processing completed: {}/{} files processed, {:.1}% success rate",
            total_processed, total_files, success_rate
        );

        Ok(results)
    }

    /// Process a large file using streaming approach with chunked reading
    async fn process_large_file_with_streaming(
        parser: &TreeSitterParser,
        file_path: &std::path::Path,
        config: &StreamingAnalysisConfig,
    ) -> Result<FileAnalysis, ParseError> {
        use tokio::io::AsyncReadExt;

        tracing::info!(
            "Processing large file with streaming: {}",
            file_path.display()
        );

        let file_size = tokio::fs::metadata(file_path)
            .await
            .map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Failed to get file metadata: {}", e),
                position: None,
            })?
            .len();

        // If file is extremely large, use chunked processing
        if file_size > config.max_file_size / 10 {
            return Self::process_file_in_chunks(parser, file_path, config).await;
        }

        // For moderately large files, use buffered reading
        let mut file = tokio::fs::File::open(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to open file: {}", e),
            position: None,
        })?;

        let mut content = String::new();
        let mut buffer = vec![0u8; 64 * 1024]; // 64KB buffer
        let mut total_read = 0u64;

        // Read file in chunks to avoid memory spikes
        while total_read < file_size {
            let bytes_read = file.read(&mut buffer).await.map_err(|e| ParseError {
                file_path: file_path.to_string_lossy().to_string(),
                error_type: ParseErrorType::Other,
                message: format!("Failed to read file chunk: {}", e),
                position: None,
            })?;

            if bytes_read == 0 {
                break;
            }

            let chunk_str = String::from_utf8_lossy(&buffer[..bytes_read]);
            content.push_str(&chunk_str);
            total_read += bytes_read as u64;

            // Check memory pressure periodically
            if total_read % (64 * 1024 * 100) == 0 { // 64KB * 100
                // Small yield to allow other tasks to run
                tokio::task::yield_now().await;
            }
        }

        // Parse the complete content
        parser.parse_content(file_path, &content).await
    }

    /// Process extremely large files in chunks
    async fn process_file_in_chunks(
        _parser: &TreeSitterParser,
        file_path: &std::path::Path,
        _config: &StreamingAnalysisConfig,
    ) -> Result<FileAnalysis, ParseError> {
        use tokio::io::AsyncBufReadExt;

        tracing::info!(
            "Processing extremely large file in chunks: {}",
            file_path.display()
        );

        let file = tokio::fs::File::open(file_path).await.map_err(|e| ParseError {
            file_path: file_path.to_string_lossy().to_string(),
            error_type: ParseErrorType::Other,
            message: format!("Failed to open file: {}", e),
            position: None,
        })?;

        let mut reader = tokio::io::BufReader::new(file);
        let mut line_count = 0;
        let mut total_chars = 0;
        let mut content_sample = String::new();
        let mut first_chunk = true;

        // Read file line by line to avoid memory issues
        let mut line = String::new();
        while reader.read_line(&mut line).await.unwrap_or(0) > 0 {
            line_count += 1;
            total_chars += line.len();

            // Keep a sample of the content for analysis (first 10KB)
            if first_chunk && content_sample.len() < 10_000 {
                content_sample.push_str(&line);
            }

            if content_sample.len() >= 10_000 {
                first_chunk = false;
            }

            line.clear();

            // Yield periodically to prevent blocking
            if line_count % 1000 == 0 {
                tokio::task::yield_now().await;
            }
        }

        // Create a lightweight analysis based on the sample
        // Get language from file extension
        let language = match file_path.extension().and_then(|ext| ext.to_str()) {
            Some("rs") => "rust".to_string(),
            Some("py") => "python".to_string(),
            Some("js") => "javascript".to_string(),
            Some("ts") => "typescript".to_string(),
            _ => "unknown".to_string(),
        };
        let content_hash = {
            use sha2::{Digest, Sha256};
            let mut hasher = Sha256::new();
            hasher.update(content_sample.as_bytes());
            format!("{:x}", hasher.finalize())
        };

        let analysis = FileAnalysis {
            path: file_path.to_string_lossy().to_string(),
            language,
            content_hash,
            size_bytes: Some(total_chars as u64),
            ast: crate::models::AstNode {
                node_type: "large_file_placeholder".to_string(),
                name: None,
                range: crate::models::Range {
                    start: crate::models::Position {
                        line: 0,
                        column: 0,
                        byte: 0,
                    },
                    end: crate::models::Position {
                        line: line_count,
                        column: 0,
                        byte: total_chars as u32,
                    },
                },
                children: vec![],
                properties: None,
                text: Some(format!("Large file with {} lines", line_count)),
            },
            metrics: crate::models::FileMetrics {
                lines_of_code: line_count as u32,
                total_lines: Some(line_count as u32),
                complexity: 1,              // Minimal complexity for large files
                maintainability_index: 50.0, // Neutral score
                function_count: 0,
                class_count: 0,
                comment_ratio: 0.0,
            },
            chunks: None,
            symbols: None,
        };

        Ok(analysis)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::fs;

    #[tokio::test]
    async fn test_streaming_config() {
        let parser = Arc::new(TreeSitterParser::new().unwrap());
        let processor = StreamingProcessor::new(parser, None);

        // Verify streaming config is properly set
        let streaming_config = StreamingAnalysisConfig {
            max_file_size: 3 * 1024 * 1024 * 1024, // 3GB max file size
            file_timeout: std::time::Duration::from_secs(300), // 5 minutes timeout
            collect_metrics: true,
            extract_symbols: true,
            extract_chunks: true,
            max_ast_depth: 100,
        };

        assert_eq!(streaming_config.repository_threshold, 1024 * 1024 * 1024);
        assert_eq!(streaming_config.file_threshold, 10 * 1024 * 1024);
        assert!(streaming_config.incremental_parsing);
    }

    #[tokio::test]
    async fn test_large_file_detection() {
        let temp_dir = TempDir::new().unwrap();
        let file_path = temp_dir.path().join("large_file.txt");

        // Create a file larger than threshold
        let large_content = "x".repeat(11 * 1024 * 1024); // 11MB
        fs::write(&file_path, large_content).unwrap();

        let metadata = tokio::fs::metadata(&file_path).await.unwrap();
        assert!(metadata.len() > 10 * 1024 * 1024); // Verify it's larger than 10MB threshold
    }
}