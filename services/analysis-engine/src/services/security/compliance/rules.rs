//! Compliance rules database

use crate::models::security::{ComplianceFramework, SecuritySeverity};
use once_cell::sync::Lazy;
use regex::Regex;
use std::collections::HashMap;

/// Rule for compliance checking
#[derive(Debug)]
pub struct ComplianceRule {
    pub id: String,
    pub name: String,
    pub description: String,
    pub regex: &'static Regex,
    pub severity: SecuritySeverity,
    pub category: String,
    pub remediation: Option<String>,
    pub business_impact: Option<String>,
    pub technical_debt_hours: Option<f64>,
}

impl ComplianceRule {
    pub fn find_violations(&self, content: &str) -> Vec<(usize, String)> {
        let mut violations = Vec::new();
        
        for (line_num, line) in content.lines().enumerate() {
            if self.regex.is_match(line) {
                violations.push((line_num + 1, line.to_string()));
            }
        }
        
        violations
    }
}

/// Load all compliance rules for each framework
pub fn load_compliance_rules() -> HashMap<ComplianceFramework, Vec<ComplianceRule>> {
    let mut rules = HashMap::new();
    
    // PCI-DSS Rules
    rules.insert(ComplianceFramework::PciDss, load_pci_dss_rules());
    
    // GDPR Rules
    rules.insert(ComplianceFramework::Gdpr, load_gdpr_rules());
    
    // HIPAA Rules
    rules.insert(ComplianceFramework::Hipaa, load_hipaa_rules());
    
    // SOC2 Rules
    rules.insert(ComplianceFramework::Soc2, load_soc2_rules());
    
    // ISO 27001 Rules
    rules.insert(ComplianceFramework::Iso27001, load_iso27001_rules());
    
    // NIST Rules
    rules.insert(ComplianceFramework::Nist, load_nist_rules());
    
    // CIS Rules
    rules.insert(ComplianceFramework::Cis, load_cis_rules());
    
    // OWASP Rules
    rules.insert(ComplianceFramework::Owasp, load_owasp_rules());
    
    rules
}

fn load_pci_dss_rules() -> Vec<ComplianceRule> {
    static PAN_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r#"\b[3-6]\d{3}[\s\-]?\d{4}[\s\-]?\d{4}[\s\-]?\d{3,4}\b"#).unwrap()
    });
    static PAN_STORAGE_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r#"(?i)(store|save|write).*(card|pan|credit).*number"#).unwrap()
    });
    static WEAK_CRYPTO_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"(?i)(md5|sha1|des\b|rc4)"#).unwrap());

    vec![
        ComplianceRule {
            id: "PCI-DSS-3.2".to_string(),
            name: "No Hardcoded PAN".to_string(),
            description: "Primary Account Numbers (PAN) should not be hardcoded in source code"
                .to_string(),
            regex: &PAN_REGEX,
            severity: SecuritySeverity::Critical,
            category: "Data Protection".to_string(),
            remediation: Some(
                "Remove hardcoded card numbers and use tokenization or secure vault storage"
                    .to_string(),
            ),
            business_impact: Some(
                "Potential PCI compliance violation and data breach risk".to_string(),
            ),
            technical_debt_hours: Some(4.0),
        },
        ComplianceRule {
            id: "PCI-DSS-3.4".to_string(),
            name: "PAN Storage Must Be Encrypted".to_string(),
            description: "Card data must be encrypted when stored".to_string(),
            regex: &PAN_STORAGE_REGEX,
            severity: SecuritySeverity::High,
            category: "Data Protection".to_string(),
            remediation: Some("Implement strong encryption for stored card data".to_string()),
            business_impact: Some("Non-compliance with PCI-DSS storage requirements".to_string()),
            technical_debt_hours: Some(8.0),
        },
        ComplianceRule {
            id: "PCI-DSS-4.1".to_string(),
            name: "Use Strong Cryptography".to_string(),
            description: "Use strong cryptography and security protocols".to_string(),
            regex: &WEAK_CRYPTO_REGEX,
            severity: SecuritySeverity::High,
            category: "Cryptography".to_string(),
            remediation: Some("Use AES-256, SHA-256 or stronger algorithms".to_string()),
            business_impact: Some("Weak encryption may not meet PCI-DSS requirements".to_string()),
            technical_debt_hours: Some(6.0),
        },
    ]
}

fn load_gdpr_rules() -> Vec<ComplianceRule> {
    static PERSONAL_DATA_LOGGING_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"(?i)log.*(email|phone|ssn|address|ip)"#).unwrap());
    static CONSENT_TRACKING_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"(?i)collect.*(user|personal|customer).*data"#).unwrap());

    vec![
        ComplianceRule {
            id: "GDPR-Art-5".to_string(),
            name: "Data Minimization".to_string(),
            description: "Avoid logging personal data unnecessarily".to_string(),
            regex: &PERSONAL_DATA_LOGGING_REGEX,
            severity: SecuritySeverity::Medium,
            category: "Privacy".to_string(),
            remediation: Some("Mask or redact personal data in logs".to_string()),
            business_impact: Some(
                "GDPR violation - excessive personal data processing".to_string(),
            ),
            technical_debt_hours: Some(2.0),
        },
        ComplianceRule {
            id: "GDPR-Art-7".to_string(),
            name: "Consent Required".to_string(),
            description: "Personal data collection requires explicit consent".to_string(),
            regex: &CONSENT_TRACKING_REGEX,
            severity: SecuritySeverity::High,
            category: "Privacy".to_string(),
            remediation: Some("Implement consent management and tracking".to_string()),
            business_impact: Some("Processing without consent violates GDPR".to_string()),
            technical_debt_hours: Some(16.0),
        },
    ]
}

fn load_hipaa_rules() -> Vec<ComplianceRule> {
    static PHI_IN_URLS_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"(?i)(patient|medical|health).*(id|ssn|record)"#).unwrap());
    static AUDIT_LOGGING_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"(?i)access.*(patient|medical|health).*record"#).unwrap());

    vec![
        ComplianceRule {
            id: "HIPAA-164.312".to_string(),
            name: "PHI Protection".to_string(),
            description: "Protected Health Information must be secured".to_string(),
            regex: &PHI_IN_URLS_REGEX,
            severity: SecuritySeverity::Critical,
            category: "Healthcare Privacy".to_string(),
            remediation: Some("Encrypt PHI and implement access controls".to_string()),
            business_impact: Some("HIPAA violation - unprotected PHI".to_string()),
            technical_debt_hours: Some(8.0),
        },
        ComplianceRule {
            id: "HIPAA-164.308".to_string(),
            name: "Access Logging Required".to_string(),
            description: "Access to PHI must be logged".to_string(),
            regex: &AUDIT_LOGGING_REGEX,
            severity: SecuritySeverity::High,
            category: "Audit Controls".to_string(),
            remediation: Some(
                "Implement comprehensive audit logging for PHI access".to_string(),
            ),
            business_impact: Some("Missing required HIPAA audit controls".to_string()),
            technical_debt_hours: Some(12.0),
        },
    ]
}

fn load_soc2_rules() -> Vec<ComplianceRule> {
    static SECURE_SESSION_MANAGEMENT_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"(?i)session.*(timeout|expire)"#).unwrap());

    vec![ComplianceRule {
        id: "SOC2-CC6.1".to_string(),
        name: "Session Management".to_string(),
        description: "Implement secure session management".to_string(),
        regex: &SECURE_SESSION_MANAGEMENT_REGEX,
        severity: SecuritySeverity::Medium,
        category: "Access Control".to_string(),
        remediation: Some("Implement session timeouts and secure session handling".to_string()),
        business_impact: Some("Inadequate session management for SOC2".to_string()),
        technical_debt_hours: Some(4.0),
    }]
}

fn load_iso27001_rules() -> Vec<ComplianceRule> {
    static PASSWORD_COMPLEXITY_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"(?i)password.*validation|password.*regex"#).unwrap());

    vec![ComplianceRule {
        id: "ISO27001-A.9.4.3".to_string(),
        name: "Password Management".to_string(),
        description: "Enforce strong password policies".to_string(),
        regex: &PASSWORD_COMPLEXITY_REGEX,
        severity: SecuritySeverity::Medium,
        category: "Access Control".to_string(),
        remediation: Some("Implement password complexity requirements".to_string()),
        business_impact: Some("Weak password policy non-compliance".to_string()),
        technical_debt_hours: Some(3.0),
    }]
}

fn load_nist_rules() -> Vec<ComplianceRule> {
    static SECURE_COMMUNICATION_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"(?i)http://(?!localhost|127\.0\.0\.1)"#).unwrap());

    vec![ComplianceRule {
        id: "NIST-SC-8".to_string(),
        name: "Transmission Confidentiality".to_string(),
        description: "Use encrypted communication channels".to_string(),
        regex: &SECURE_COMMUNICATION_REGEX,
        severity: SecuritySeverity::High,
        category: "Network Security".to_string(),
        remediation: Some("Use HTTPS for all external communications".to_string()),
        business_impact: Some("Unencrypted data transmission".to_string()),
        technical_debt_hours: Some(2.0),
    }]
}

fn load_cis_rules() -> Vec<ComplianceRule> {
    static DEFAULT_CREDENTIALS_REGEX: Lazy<Regex> = Lazy::new(|| {
        Regex::new(r#"(?i)(admin|root|test):(admin|root|test|password|123456)"#).unwrap()
    });

    vec![ComplianceRule {
        id: "CIS-2.1".to_string(),
        name: "No Default Credentials".to_string(),
        description: "Default credentials must be changed".to_string(),
        regex: &DEFAULT_CREDENTIALS_REGEX,
        severity: SecuritySeverity::Critical,
        category: "Authentication".to_string(),
        remediation: Some("Remove all default credentials".to_string()),
        business_impact: Some("System vulnerable to unauthorized access".to_string()),
        technical_debt_hours: Some(1.0),
    }]
}

fn load_owasp_rules() -> Vec<ComplianceRule> {
    static SQL_INJECTION_PREVENTION_REGEX: Lazy<Regex> =
        Lazy::new(|| Regex::new(r#"(?i)query.*\+.*user|execute.*\+.*input"#).unwrap());

    vec![ComplianceRule {
        id: "OWASP-A03".to_string(),
        name: "Injection Prevention".to_string(),
        description: "Prevent injection attacks".to_string(),
        regex: &SQL_INJECTION_PREVENTION_REGEX,
        severity: SecuritySeverity::High,
        category: "Injection".to_string(),
        remediation: Some("Use parameterized queries".to_string()),
        business_impact: Some("Application vulnerable to injection attacks".to_string()),
        technical_debt_hours: Some(4.0),
    }]
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_rule_loading() {
        let rules = load_compliance_rules();
        
        // Ensure we have rules for each framework
        assert!(rules.contains_key(&ComplianceFramework::PciDss));
        assert!(rules.contains_key(&ComplianceFramework::Gdpr));
        assert!(rules.contains_key(&ComplianceFramework::Hipaa));
        
        // Check PCI-DSS has some rules
        let pci_rules = rules.get(&ComplianceFramework::PciDss).unwrap();
        assert!(!pci_rules.is_empty());
    }
    
    #[test]
    fn test_violation_detection() {
        let rules = load_pci_dss_rules();
        if let Some(pan_rule) = rules.iter().find(|r| r.id == "PCI-DSS-3.2") {
            let test_content = "card_number = '****************'";
            let violations = pan_rule.find_violations(test_content);
            assert!(!violations.is_empty());
        }
    }
}