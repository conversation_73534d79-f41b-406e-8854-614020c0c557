//! Core dependency scanning logic

use super::vulnerability_check::{DependencyVulnInfo, load_vulnerability_db};
use super::parsers::*;
use crate::models::security::{
    DependencyVulnerability, PackageManager, VulnerabilitySource,
};
use crate::models::production::DependencyInfo;
use crate::models::FileAnalysis;
use crate::errors::AnalysisResult;
use std::collections::HashMap;
use uuid::Uuid;
use chrono::Utc;

/// Main dependency scanner that checks for vulnerabilities in project dependencies
#[derive(Debug)]
pub struct DependencyScanner {
    vulnerability_db: HashMap<String, Vec<DependencyVulnInfo>>,
}

impl DependencyScanner {
    pub fn new() -> Self {
        Self {
            vulnerability_db: load_vulnerability_db(),
        }
    }

    pub async fn scan_dependencies(
        &self,
        analysis_id: &str,
        file_analyses: &[FileAnalysis],
        _threat_intel_enabled: bool,
    ) -> AnalysisResult<Vec<DependencyVulnerability>> {
        let mut vulnerabilities = Vec::new();
        let dependencies = self.extract_dependencies(file_analyses).await?;

        for dependency in dependencies {
            if let Some(vulns) = self.vulnerability_db.get(&dependency.name) {
                for vuln_info in vulns {
                    if self.version_is_vulnerable(&dependency.current_version, &vuln_info.affected_versions) {
                        let vulnerability = DependencyVulnerability {
                            dependency_vuln_id: Uuid::new_v4().to_string(),
                            analysis_id: analysis_id.to_string(),
                            dependency_name: dependency.name.clone(),
                            dependency_version: dependency.current_version.clone(),
                            package_manager: self.detect_package_manager(&dependency),
                            cve_id: vuln_info.cve_id.clone(),
                            vulnerability_source: VulnerabilitySource::NationalVulnerabilityDatabase,
                            severity: vuln_info.severity.clone(),
                            cvss_score: vuln_info.cvss_score,
                            cvss_vector: vuln_info.cvss_vector.clone(),
                            description: Some(vuln_info.description.clone()),
                            published_date: vuln_info.published_date,
                            last_modified_date: vuln_info.last_modified_date,
                            affected_versions: vec![vuln_info.affected_versions.clone()],
                            patched_versions: vec![vuln_info.patched_versions.clone()],
                            workaround: vuln_info.workaround.clone(),
                            exploit_available: vuln_info.exploit_available,
                            proof_of_concept_available: vuln_info.proof_of_concept_available,
                            created_at: Utc::now(),
                            updated_at: None,
                        };
                        vulnerabilities.push(vulnerability);
                    }
                }
            }
        }

        Ok(vulnerabilities)
    }

    pub async fn extract_dependencies(&self, file_analyses: &[FileAnalysis]) -> AnalysisResult<Vec<DependencyInfo>> {
        let mut dependencies = Vec::new();

        for file_analysis in file_analyses {
            let path = &file_analysis.path;
            
            // JavaScript/Node.js
            if path.ends_with("package.json") {
                dependencies.extend(parse_package_json(file_analysis)?);
            } else if path.ends_with("package-lock.json") {
                dependencies.extend(parse_package_lock_json(file_analysis)?);
            } else if path.ends_with("yarn.lock") {
                dependencies.extend(parse_yarn_lock(file_analysis)?);
            }
            
            // Python
            else if path.ends_with("requirements.txt") {
                dependencies.extend(parse_requirements_txt(file_analysis)?);
            } else if path.ends_with("Pipfile") {
                dependencies.extend(parse_pipfile(file_analysis)?);
            } else if path.ends_with("pyproject.toml") {
                dependencies.extend(parse_pyproject_toml(file_analysis)?);
            }
            
            // Rust
            else if path.ends_with("Cargo.toml") {
                dependencies.extend(parse_cargo_toml(file_analysis)?);
            } else if path.ends_with("Cargo.lock") {
                dependencies.extend(parse_cargo_lock(file_analysis)?);
            }
            
            // Java
            else if path.ends_with("pom.xml") {
                dependencies.extend(parse_pom_xml(file_analysis)?);
            } else if path.ends_with("build.gradle") || path.ends_with("build.gradle.kts") {
                dependencies.extend(parse_gradle_build(file_analysis)?);
            }
            
            // .NET
            else if path.ends_with("packages.config") {
                dependencies.extend(parse_packages_config(file_analysis)?);
            } else if path.ends_with(".csproj") || path.ends_with(".vbproj") || path.ends_with(".fsproj") {
                dependencies.extend(parse_dotnet_project(file_analysis)?);
            }
            
            // PHP
            else if path.ends_with("composer.json") {
                dependencies.extend(parse_composer_json(file_analysis)?);
            } else if path.ends_with("composer.lock") {
                dependencies.extend(parse_composer_lock(file_analysis)?);
            }
            
            // Go
            else if path.ends_with("go.mod") {
                dependencies.extend(parse_go_mod(file_analysis)?);
            } else if path.ends_with("go.sum") {
                dependencies.extend(parse_go_sum(file_analysis)?);
            }
            
            // Ruby
            else if path.ends_with("Gemfile") {
                dependencies.extend(parse_gemfile(file_analysis)?);
            } else if path.ends_with("Gemfile.lock") {
                dependencies.extend(parse_gemfile_lock(file_analysis)?);
            }
        }

        Ok(dependencies)
    }

    fn version_is_vulnerable(&self, current_version: &str, affected_versions: &str) -> bool {
        // Simple version matching - in production, use proper semver comparison
        affected_versions.contains(current_version) || affected_versions.contains("*")
    }

    fn detect_package_manager(&self, dependency: &DependencyInfo) -> PackageManager {
        // Detect based on dependency metadata if available
        if let Some(registry) = &dependency.registry {
            match registry.as_str() {
                "npm" | "npmjs" => PackageManager::Npm,
                "pypi" => PackageManager::Pip,
                "crates.io" => PackageManager::Cargo,
                "maven" => PackageManager::Maven,
                "nuget" => PackageManager::NuGet,
                "packagist" => PackageManager::Composer,
                "rubygems" => PackageManager::Gem,
                _ => PackageManager::Other(registry.clone()),
            }
        } else {
            PackageManager::Other("unknown".to_string())
        }
    }
}

impl Default for DependencyScanner {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_version_vulnerability_check() {
        let scanner = DependencyScanner::new();
        assert!(scanner.version_is_vulnerable("1.0.0", "1.0.0"));
        assert!(scanner.version_is_vulnerable("1.0.0", "*"));
        assert!(!scanner.version_is_vulnerable("1.0.1", "1.0.0"));
    }
}