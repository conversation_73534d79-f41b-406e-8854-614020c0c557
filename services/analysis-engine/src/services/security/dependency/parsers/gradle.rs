//! Gradle build file parser (build.gradle, build.gradle.kts)

use crate::models::production::{DependencyInfo, UpdatePriority};
use crate::models::FileAnalysis;
use crate::errors::AnalysisResult;
use once_cell::sync::Lazy;
use regex::Regex;

/// Parse build.gradle or build.gradle.kts file
pub fn parse_gradle_build(file_analysis: &FileAnalysis) -> AnalysisResult<Vec<DependencyInfo>> {
    let content = file_analysis.ast.text.clone().unwrap_or_default();
    
    if content.trim().is_empty() {
        return Ok(Vec::new());
    }
    
    let mut dependencies = Vec::new();
    let is_kotlin = file_analysis.path.ends_with(".kts");
    
    // Regex patterns for Gradle dependencies
    let patterns = if is_kotlin {
        vec![
            // Kotlin DSL patterns
            (r#"implementation\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#, "implementation"),
            (r#"testImplementation\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#, "test"),
            (r#"api\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#, "api"),
            (r#"compileOnly\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#, "compileOnly"),
            (r#"runtimeOnly\s*\(\s*"([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^"]+)"\s*\)"#, "runtime"),
        ]
    } else {
        vec![
            // Groovy DSL patterns
            (r#"implementation\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#, "implementation"),
            (r#"testImplementation\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#, "test"),
            (r#"api\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#, "api"),
            (r#"compile\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#, "compile"),
            (r#"compileOnly\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#, "compileOnly"),
            (r#"runtime\s+['"]([a-zA-Z0-9._-]+):([a-zA-Z0-9._-]+):([^'"]+)['"]"#, "runtime"),
        ]
    };
    
    for (pattern_str, scope) in patterns {
       let regex = Lazy::new(|| Regex::new(pattern_str).unwrap());
       for captures in regex.captures_iter(&content) {
           if let (Some(group), Some(artifact), Some(version)) = (
               captures.get(1).map(|m| m.as_str()),
               captures.get(2).map(|m| m.as_str()),
               captures.get(3).map(|m| m.as_str()),
           ) {
               let name = format!("{}:{}", group, artifact);
               let is_dev = scope == "test";
               
               dependencies.push(DependencyInfo {
                   name,
                   current_version: clean_gradle_version(version),
                   latest_version: String::new(),
                   vulnerability_count: 0,
                   update_priority: UpdatePriority::Minor,
                   registry: Some("maven".to_string()),
                   license: None,
                   description: None,
                   homepage: None,
                   repository_url: None,
                   is_direct: true,
                   is_dev,
                   is_optional: false,
                   dependency_path: None,
               });
           }
       }
    }
    
    // Also look for platform/BOM dependencies
    let platform_patterns = if is_kotlin {
        vec![
            r#"implementation\s*\(\s*platform\s*\(\s*"([^"]+)"\s*\)\s*\)"#,
            r#"implementation\s*\(\s*enforcedPlatform\s*\(\s*"([^"]+)"\s*\)\s*\)"#,
        ]
    } else {
        vec![
            r#"implementation\s+platform\s*\(\s*['"]([^'"]+)['"]\s*\)"#,
            r#"implementation\s+enforcedPlatform\s*\(\s*['"]([^'"]+)['"]\s*\)"#,
        ]
    };
    
    for pattern_str in platform_patterns {
       let regex = Lazy::new(|| Regex::new(pattern_str).unwrap());
       for captures in regex.captures_iter(&content) {
           if let Some(dep) = captures.get(1).map(|m| m.as_str()) {
               // Parse the dependency string (group:artifact:version)
               let parts: Vec<&str> = dep.split(':').collect();
               if parts.len() >= 3 {
                   let name = format!("{}:{}", parts[0], parts[1]);
                   let version = parts[2];
                   
                   dependencies.push(DependencyInfo {
                       name,
                       current_version: clean_gradle_version(version),
                       latest_version: String::new(),
                       vulnerability_count: 0,
                       update_priority: UpdatePriority::Minor,
                       registry: Some("maven".to_string()),
                       license: None,
                       description: None,
                       homepage: None,
                       repository_url: None,
                       is_direct: true,
                       is_dev: false,
                       is_optional: false,
                       dependency_path: None,
                   });
               }
           }
       }
    }
    
    Ok(dependencies)
}

/// Clean Gradle version string
fn clean_gradle_version(version: &str) -> String {
    // Remove variable placeholders if any
    if version.starts_with('$') {
        return version.to_string(); // Keep as is for variable references
    }
    
    version.trim().to_string()
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_clean_gradle_version() {
        assert_eq!(clean_gradle_version("1.0.0"), "1.0.0");
        assert_eq!(clean_gradle_version("$springVersion"), "$springVersion");
        assert_eq!(clean_gradle_version(" 2.3.4 "), "2.3.4");
    }
}