//! Risk assessment logic

use crate::models::security::{
    SecurityAssessment, SecurityVulnerability, DependencyVulnerability, DetectedSecret,
    ComplianceViolation, SecuritySeverity, RiskLevel, TrendingDirection,
};
use crate::errors::AnalysisResult;
use uuid::Uuid;
use chrono::Utc;

/// Assesses overall security risk and calculates scores
pub struct RiskAssessor {}

impl RiskAssessor {
    pub fn new() -> Self {
        Self {}
    }

    pub async fn assess_security(
        &self,
        analysis_id: &str,
        vulnerabilities: &[SecurityVulnerability],
        dependency_vulnerabilities: &[DependencyVulnerability],
        secrets: &[DetectedSecret],
        compliance_violations: &[ComplianceViolation],
    ) -> AnalysisResult<SecurityAssessment> {
        let total_vulns = vulnerabilities.len();
        let critical_vulns = vulnerabilities.iter()
            .filter(|v| v.severity == SecuritySeverity::Critical)
            .count();
        let high_vulns = vulnerabilities.iter()
            .filter(|v| v.severity >= SecuritySeverity::High)
            .count();
        let total_dep_vulns = dependency_vulnerabilities.len();
        let high_dep_vulns = dependency_vulnerabilities.iter()
            .filter(|v| v.severity >= SecuritySeverity::High)
            .count();
        let total_secrets = secrets.len();
        let critical_secrets = secrets.iter()
            .filter(|s| s.severity == SecuritySeverity::Critical)
            .count();

        let score = 100.0
            - (total_vulns as f64 * 0.5)
            - (high_vulns as f64 * 2.0)
            - (total_dep_vulns as f64 * 0.2)
            - (high_dep_vulns as f64 * 1.5)
            - (total_secrets as f64 * 5.0)
            - (critical_secrets as f64 * 10.0)
            - (compliance_violations.len() as f64 * 1.0);

        let overall_security_score = score.max(0.0);

        let risk_level_enum = if overall_security_score < 50.0 {
            RiskLevel::Critical
        } else if overall_security_score < 70.0 {
            RiskLevel::High
        } else if overall_security_score < 90.0 {
            RiskLevel::Medium
        } else {
            RiskLevel::Low
        };

        Ok(SecurityAssessment {
            assessment_id: Uuid::new_v4().to_string(),
            analysis_id: analysis_id.to_string(),
            overall_security_score,
            vulnerability_score: 100.0 - (total_vulns as f64 * 0.5) - (high_vulns as f64 * 2.0),
            dependency_score: 100.0 - (total_dep_vulns as f64 * 0.2) - (high_dep_vulns as f64 * 1.5),
            secrets_score: 100.0 - (total_secrets as f64 * 5.0) - (critical_secrets as f64 * 10.0),
            compliance_score: 100.0 - (compliance_violations.len() as f64 * 1.0),
            risk_level: risk_level_enum,
            total_vulnerabilities: total_vulns as i64,
            critical_vulnerabilities: critical_vulns as i64,
            high_vulnerabilities: high_vulns as i64,
            medium_vulnerabilities: (total_vulns - critical_vulns - high_vulns) as i64,
            low_vulnerabilities: 0,
            total_secrets_found: total_secrets as i64,
            high_entropy_secrets: critical_secrets as i64,
            compliance_violations_count: compliance_violations.len() as i64,
            security_debt_score: None,
            improvement_recommendations: vec![
                "Prioritize critical and high severity vulnerabilities.".to_string(),
                "Regularly update dependencies.".to_string(),
            ],
            recommendations: vec![
                "Implement security scanning in CI/CD pipeline.".to_string(),
                "Establish security code review process.".to_string(),
            ],
            detailed_findings: vec![
                format!("Found {} total vulnerabilities", total_vulns),
                format!("Found {} secrets", total_secrets),
                format!("Found {} compliance violations", compliance_violations.len()),
            ],
            risk_matrix: Some("High-impact vulnerabilities combined with exposed secrets create elevated risk.".to_string()),
            trending_direction: TrendingDirection::Stable,
            created_at: Utc::now(),
            updated_at: None,
        })
    }
}

impl Default for RiskAssessor {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_risk_assessment_calculation() {
        let assessor = RiskAssessor::new();
        
        // Test with no vulnerabilities
        let assessment = assessor.assess_security(
            "test-analysis-1",
            &[],
            &[],
            &[],
            &[],
        ).await.unwrap();
        
        assert_eq!(assessment.overall_security_score, 100.0);
        assert_eq!(assessment.risk_level, RiskLevel::Low);
        
        // Test with some vulnerabilities
        let vuln = SecurityVulnerability {
            vulnerability_id: "test-vuln-1".to_string(),
            analysis_id: "test-analysis-1".to_string(),
            file_path: "test.rs".to_string(),
            line_start: 10,
            line_end: 10,
            vulnerability_type: crate::models::security::VulnerabilityType::SqlInjection,
            severity: SecuritySeverity::Critical,
            confidence_score: 0.9,
            description: "Test vulnerability".to_string(),
            code_snippet: "test code".to_string(),
            remediation: Some("Fix it".to_string()),
            cwe_id: Some("CWE-89".to_string()),
            owasp_category: Some("A03".to_string()),
            attack_vector: Some("Network".to_string()),
            exploitability_score: Some(9.8),
            impact_score: Some(0.1),
            source: crate::models::security::VulnerabilitySource::SAST,
            metadata: None,
            detected_at: Utc::now(),
            updated_at: None,
            cve_id: None,
        };
        
        let assessment = assessor.assess_security(
            "test-analysis-1",
            &[vuln],
            &[],
            &[],
            &[],
        ).await.unwrap();
        
        assert!(assessment.overall_security_score < 100.0);
        assert_eq!(assessment.critical_vulnerabilities, 1);
    }
}