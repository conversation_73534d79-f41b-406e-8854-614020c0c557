use anyhow::Result;
use crate::models::{FileAnalysis, CodeEmbedding};
use crate::services::embeddings_enhancement::{EnhancedEmbeddingsService, FeatureToggles};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SemanticSearchResult {
    pub query: String,
    pub results: Vec<SearchResult>,
    pub total_results: usize,
    pub search_time_ms: u64,
    pub query_embedding: Option<Vec<f32>>,
    pub search_metadata: SearchMetadata,
    pub suggestions: Vec<String>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchResult {
    pub file_path: String,
    pub language: String,
    pub similarity_score: f32,
    pub relevance_score: f32,
    pub chunk_id: String,
    pub content_preview: String,
    pub line_range: Option<LineRange>,
    pub symbol_context: Option<SymbolContext>,
    pub code_snippet: Option<String>,
    pub explanation: Option<String>,
    pub ranking_factors: RankingFactors,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LineRange {
    pub start_line: u32,
    pub end_line: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SymbolContext {
    pub symbol_name: String,
    pub symbol_type: String,
    pub containing_function: Option<String>,
    pub containing_class: Option<String>,
    pub namespace: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct RankingFactors {
    pub similarity_score: f32,
    pub language_match: f32,
    pub symbol_relevance: f32,
    pub code_quality: f32,
    pub recency: f32,
    pub file_importance: f32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SearchMetadata {
    pub total_embeddings_searched: usize,
    pub filtered_results: usize,
    pub search_strategy: String,
    pub query_processing_time_ms: u64,
    pub similarity_threshold: f32,
    pub max_results: usize,
    pub language_filters: Vec<String>,
    pub file_type_filters: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SemanticSearchQuery {
    pub query: String,
    pub language_filters: Vec<String>,
    pub file_type_filters: Vec<String>,
    pub max_results: usize,
    pub similarity_threshold: f32,
    pub include_code_snippets: bool,
    pub include_explanations: bool,
    pub search_strategy: SearchStrategy,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum SearchStrategy {
    ExactMatch,
    SemanticSimilarity,
    Hybrid,
    FuzzyMatch,
}

impl Default for SemanticSearchQuery {
    fn default() -> Self {
        Self {
            query: String::new(),
            language_filters: Vec::new(),
            file_type_filters: Vec::new(),
            max_results: 20,
            similarity_threshold: 0.7,
            include_code_snippets: true,
            include_explanations: false,
            search_strategy: SearchStrategy::SemanticSimilarity,
        }
    }
}

#[derive(Debug, Default)]
pub struct SearchMetrics {
    total_searches: u64,
    successful_searches: u64,
    failed_searches: u64,
    average_search_time_ms: f64,
    average_results_per_search: f64,
    cache_hits: u64,
    cache_misses: u64,
}

pub struct SemanticSearchService {
    embeddings_service: Arc<EnhancedEmbeddingsService>,
    feature_toggles: Arc<FeatureToggles>,
    search_cache: Arc<Mutex<HashMap<String, SemanticSearchResult>>>,
    metrics: Arc<Mutex<SearchMetrics>>,
    file_analyses: Arc<Mutex<HashMap<String, FileAnalysis>>>,
    embeddings_index: Arc<Mutex<HashMap<String, CodeEmbedding>>>,
}

impl SemanticSearchService {
    pub async fn new(embeddings_service: Arc<EnhancedEmbeddingsService>) -> Result<Self> {
        let feature_toggles = embeddings_service.get_feature_toggles();

        Ok(Self {
            embeddings_service,
            feature_toggles,
            search_cache: Arc::new(Mutex::new(HashMap::new())),
            metrics: Arc::new(Mutex::new(SearchMetrics::default())),
            file_analyses: Arc::new(Mutex::new(HashMap::new())),
            embeddings_index: Arc::new(Mutex::new(HashMap::new())),
        })
    }

    pub async fn index_files(&self, analyses: &[FileAnalysis], embeddings: &[CodeEmbedding]) -> Result<()> {
        let mut file_analyses = self.file_analyses.lock().await;
        let mut embeddings_index = self.embeddings_index.lock().await;

        // Index file analyses
        for analysis in analyses {
            file_analyses.insert(analysis.path.clone(), analysis.clone());
        }

        // Index embeddings
        for embedding in embeddings {
            embeddings_index.insert(embedding.chunk_id.clone(), embedding.clone());
        }

        tracing::info!("Indexed {} files and {} embeddings", analyses.len(), embeddings.len());
        Ok(())
    }

    pub async fn search(&self, query: SemanticSearchQuery) -> Result<SemanticSearchResult> {
        if !self.feature_toggles.enable_semantic_search {
            tracing::info!("Semantic search disabled by feature toggle");
            return Ok(self.create_empty_result(&query));
        }

        let start_time = std::time::Instant::now();

        // Check cache first
        let cache_key = self.generate_cache_key(&query);
        if let Some(cached_result) = self.get_cached_result(&cache_key).await {
            self.record_cache_hit().await;
            return Ok(cached_result);
        }

        self.record_cache_miss().await;

        // Perform the search
        let result = match self.perform_semantic_search(&query).await {
            Ok(result) => {
                self.record_successful_search(start_time.elapsed().as_millis() as f64, result.results.len()).await;
                result
            }
            Err(e) => {
                self.record_failed_search().await;
                tracing::error!("Semantic search failed: {}", e);
                self.create_empty_result(&query)
            }
        };

        // Cache the result
        self.cache_result(&cache_key, &result).await;

        Ok(result)
    }

    async fn perform_semantic_search(&self, query: &SemanticSearchQuery) -> Result<SemanticSearchResult> {
        let search_start = std::time::Instant::now();

        // Generate query embedding
        let query_embedding = self.generate_query_embedding(&query.query).await?;

        // Get all embeddings for search
        let embeddings_index = self.embeddings_index.lock().await;
        let file_analyses = self.file_analyses.lock().await;

        let mut search_results = Vec::new();
        let mut total_searched = 0;

        // Calculate similarities
        for (chunk_id, embedding) in embeddings_index.iter() {
            total_searched += 1;

            // Calculate similarity
            let similarity = self.calculate_cosine_similarity(&query_embedding, &embedding.vector);

            // Apply similarity threshold
            if similarity < query.similarity_threshold {
                continue;
            }

            // Find corresponding file analysis
            let file_analysis = self.find_file_analysis_for_chunk(chunk_id, &file_analyses);

            // Apply filters
            if !self.passes_filters(&query, &file_analysis) {
                continue;
            }

            // Create search result
            let result = self.create_search_result(
                chunk_id,
                embedding,
                &file_analysis,
                similarity,
                &query,
            ).await?;

            search_results.push(result);
        }

        // Sort by relevance
        search_results.sort_by(|a, b| {
            b.relevance_score.partial_cmp(&a.relevance_score).unwrap_or(std::cmp::Ordering::Equal)
        });

        // Limit results
        search_results.truncate(query.max_results);

        // Generate suggestions
        let suggestions = self.generate_search_suggestions(&query.query, &search_results);

        let search_time_ms = search_start.elapsed().as_millis() as u64;

        Ok(SemanticSearchResult {
            query: query.query.clone(),
            results: search_results.clone(),
            total_results: search_results.len(),
            search_time_ms,
            query_embedding: Some(query_embedding),
            search_metadata: SearchMetadata {
                total_embeddings_searched: total_searched,
                filtered_results: search_results.len(),
                search_strategy: format!("{:?}", query.search_strategy),
                query_processing_time_ms: search_time_ms,
                similarity_threshold: query.similarity_threshold,
                max_results: query.max_results,
                language_filters: query.language_filters.clone(),
                file_type_filters: query.file_type_filters.clone(),
            },
            suggestions,
            timestamp: Utc::now(),
        })
    }

    async fn generate_query_embedding(&self, query: &str) -> Result<Vec<f32>> {
        // Create a temporary file analysis for the query
        let query_analysis = FileAnalysis {
            path: "query.txt".to_string(),
            language: "text".to_string(),
            content_hash: {
                use std::collections::hash_map::DefaultHasher;
                use std::hash::{Hash, Hasher};
                let mut hasher = DefaultHasher::new();
                query.hash(&mut hasher);
                format!("{:x}", hasher.finish())
            },
            size_bytes: Some(query.len() as u64),
            ast: crate::models::AstNode {
                node_type: "text".to_string(),
                name: None,
                range: crate::models::Range {
                    start: crate::models::Position { line: 0, column: 0, byte: 0 },
                    end: crate::models::Position { line: 0, column: query.len() as u32, byte: query.len() as u32 },
                },
                children: vec![],
                properties: None,
                text: Some(query.to_string()),
            },
            metrics: crate::models::FileMetrics {
                lines_of_code: 1,
                total_lines: Some(1),
                complexity: 1,
                maintainability_index: 100.0,
                function_count: 0,
                class_count: 0,
                comment_ratio: 0.0,
            },
            chunks: None,
            symbols: None,
        };

        // Generate embedding for the query using the embeddings service
        let embeddings = self.embeddings_service.generate_enhanced_embeddings(&[query_analysis]).await?;
        
        if embeddings.is_empty() {
            return Err(anyhow::anyhow!("Failed to generate query embedding"));
        }

        Ok(embeddings[0].vector.clone())
    }

    fn calculate_cosine_similarity(&self, a: &[f32], b: &[f32]) -> f32 {
        let dot_product: f32 = a.iter().zip(b.iter()).map(|(x, y)| x * y).sum();
        let norm_a: f32 = a.iter().map(|x| x * x).sum::<f32>().sqrt();
        let norm_b: f32 = b.iter().map(|x| x * x).sum::<f32>().sqrt();
        
        if norm_a == 0.0 || norm_b == 0.0 {
            0.0
        } else {
            dot_product / (norm_a * norm_b)
        }
    }

    fn find_file_analysis_for_chunk(
        &self,
        chunk_id: &str,
        file_analyses: &HashMap<String, FileAnalysis>,
    ) -> Option<FileAnalysis> {
        // Extract file path from chunk_id if it follows a pattern like "path/to/file.rs_chunk_0"
        if let Some(underscore_pos) = chunk_id.rfind("_chunk_") {
            let file_path = &chunk_id[..underscore_pos];
            if let Some(analysis) = file_analyses.get(file_path) {
                return Some(analysis.clone());
            }
        }
        
        // Fallback: look for any file analysis that might contain this chunk
        for (file_path, analysis) in file_analyses {
            if chunk_id.contains(file_path) || file_path.contains(chunk_id) {
                return Some(analysis.clone());
            }
        }
        
        // Last resort: return the first available analysis
        file_analyses.values().next().cloned()
    }

    fn passes_filters(&self, query: &SemanticSearchQuery, file_analysis: &Option<FileAnalysis>) -> bool {
        if let Some(analysis) = file_analysis {
            // Language filter
            if !query.language_filters.is_empty() && !query.language_filters.contains(&analysis.language) {
                return false;
            }

            // File type filter
            if !query.file_type_filters.is_empty() {
                let file_extension = analysis.path.split('.').last().unwrap_or("");
                if !query.file_type_filters.contains(&file_extension.to_string()) {
                    return false;
                }
            }
        }

        true
    }

    async fn create_search_result(
        &self,
        chunk_id: &str,
        _embedding: &CodeEmbedding,
        file_analysis: &Option<FileAnalysis>,
        similarity: f32,
        query: &SemanticSearchQuery,
    ) -> Result<SearchResult> {
        let (file_path, language, content_preview, symbol_context, code_snippet) = 
            if let Some(analysis) = file_analysis {
                let preview = self.generate_content_preview(analysis, &query.query);
                let symbol_context = self.extract_symbol_context(analysis, &query.query);
                let code_snippet = if query.include_code_snippets {
                    self.extract_code_snippet(analysis, &query.query)
                } else {
                    None
                };

                (analysis.path.clone(), analysis.language.clone(), preview, symbol_context, code_snippet)
            } else {
                ("Unknown".to_string(), "Unknown".to_string(), "No preview available".to_string(), None, None)
            };

        let relevance_score = self.calculate_relevance_score(similarity, file_analysis, &query.query);

        let ranking_factors = RankingFactors {
            similarity_score: similarity,
            language_match: if query.language_filters.is_empty() { 1.0 } else {
                if let Some(analysis) = file_analysis {
                    if query.language_filters.contains(&analysis.language) { 1.0 } else { 0.5 }
                } else { 0.5 }
            },
            symbol_relevance: if symbol_context.is_some() { 0.8 } else { 0.5 },
            code_quality: if let Some(analysis) = file_analysis {
                (analysis.metrics.maintainability_index / 100.0) as f32
            } else { 0.5 },
            recency: 1.0, // Could be based on file modification time
            file_importance: 0.7, // Could be based on file centrality in the codebase
        };

        let explanation = if query.include_explanations {
            Some(self.generate_explanation(&content_preview, &query.query))
        } else {
            None
        };

        Ok(SearchResult {
            file_path,
            language,
            similarity_score: similarity,
            relevance_score,
            chunk_id: chunk_id.to_string(),
            content_preview,
            line_range: None, // Could be extracted from chunk metadata
            symbol_context,
            code_snippet,
            explanation,
            ranking_factors,
        })
    }

    fn generate_content_preview(&self, analysis: &FileAnalysis, query: &str) -> String {
        if let Some(text) = &analysis.ast.text {
            // Simple preview generation - find relevant lines
            let lines: Vec<&str> = text.lines().collect();
            let query_lower = query.to_lowercase();
            
            // Find lines containing query terms
            let mut relevant_lines = Vec::new();
            for (i, line) in lines.iter().enumerate() {
                if line.to_lowercase().contains(&query_lower) {
                    relevant_lines.push((i, line));
                }
            }

            if relevant_lines.is_empty() {
                // If no direct matches, take first few lines
                lines.iter().take(3).map(|s| s.to_string()).collect::<Vec<_>>().join("\n")
            } else {
                // Show context around matches
                let mut preview = String::new();
                for (line_num, line) in relevant_lines.iter().take(3) {
                    preview.push_str(&format!("{}: {}\n", line_num + 1, line));
                }
                preview
            }
        } else {
            format!("File: {}", analysis.path)
        }
    }

    fn extract_symbol_context(&self, analysis: &FileAnalysis, query: &str) -> Option<SymbolContext> {
        if let Some(symbols) = &analysis.symbols {
            let query_lower = query.to_lowercase();
            
            // Find matching symbols
            for symbol in symbols {
                if symbol.name.to_lowercase().contains(&query_lower) {
                    return Some(SymbolContext {
                        symbol_name: symbol.name.clone(),
                        symbol_type: format!("{:?}", symbol.symbol_type),
                        containing_function: None, // Could be extracted from AST
                        containing_class: None,    // Could be extracted from AST
                        namespace: None,           // Could be extracted from AST
                    });
                }
            }
        }
        None
    }

    fn extract_code_snippet(&self, analysis: &FileAnalysis, _query: &str) -> Option<String> {
        if let Some(text) = &analysis.ast.text {
            // Simple code snippet extraction
            let lines: Vec<&str> = text.lines().collect();
            if lines.len() > 10 {
                Some(lines.iter().take(10).map(|s| s.to_string()).collect::<Vec<_>>().join("\n"))
            } else {
                Some(text.clone())
            }
        } else {
            None
        }
    }

    fn calculate_relevance_score(&self, similarity: f32, file_analysis: &Option<FileAnalysis>, query: &str) -> f32 {
        let mut score = similarity;

        // Boost score based on various factors
        if let Some(analysis) = file_analysis {
            // Boost for exact matches in file name
            if analysis.path.to_lowercase().contains(&query.to_lowercase()) {
                score += 0.2;
            }

            // Boost for high maintainability
            if analysis.metrics.maintainability_index > 80.0 {
                score += 0.1;
            }

            // Boost for symbols matching query
            if let Some(symbols) = &analysis.symbols {
                for symbol in symbols {
                    if symbol.name.to_lowercase().contains(&query.to_lowercase()) {
                        score += 0.3;
                        break;
                    }
                }
            }
        }

        // Ensure score is within [0, 1] range
        score.min(1.0).max(0.0)
    }

    fn generate_explanation(&self, _content: &str, query: &str) -> String {
        // Simple explanation generation
        format!("This code is related to your query '{}' based on content analysis. The code contains relevant functionality that matches your search criteria.", query)
    }

    fn generate_search_suggestions(&self, query: &str, results: &[SearchResult]) -> Vec<String> {
        let mut suggestions = Vec::new();

        // Generate suggestions based on query
        if query.len() < 3 {
            suggestions.push("Try using more specific terms".to_string());
        }

        // Generate suggestions based on results
        if results.is_empty() {
            suggestions.push("Try using different keywords".to_string());
            suggestions.push("Check spelling and try synonyms".to_string());
            suggestions.push("Use broader search terms".to_string());
        } else if results.len() == 1 {
            suggestions.push("Try related terms for more results".to_string());
        }

        // Language-specific suggestions
        let languages: std::collections::HashSet<_> = results.iter().map(|r| &r.language).collect();
        if languages.len() > 1 {
            suggestions.push("Filter by programming language for more specific results".to_string());
        }

        suggestions
    }

    fn create_empty_result(&self, query: &SemanticSearchQuery) -> SemanticSearchResult {
        SemanticSearchResult {
            query: query.query.clone(),
            results: Vec::new(),
            total_results: 0,
            search_time_ms: 0,
            query_embedding: None,
            search_metadata: SearchMetadata {
                total_embeddings_searched: 0,
                filtered_results: 0,
                search_strategy: format!("{:?}", query.search_strategy),
                query_processing_time_ms: 0,
                similarity_threshold: query.similarity_threshold,
                max_results: query.max_results,
                language_filters: query.language_filters.clone(),
                file_type_filters: query.file_type_filters.clone(),
            },
            suggestions: vec!["Semantic search is currently disabled".to_string()],
            timestamp: Utc::now(),
        }
    }

    // Cache management
    fn generate_cache_key(&self, query: &SemanticSearchQuery) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        query.query.hash(&mut hasher);
        query.language_filters.hash(&mut hasher);
        query.file_type_filters.hash(&mut hasher);
        query.max_results.hash(&mut hasher);
        query.similarity_threshold.to_bits().hash(&mut hasher);

        format!("search_{:x}", hasher.finish())
    }

    async fn get_cached_result(&self, cache_key: &str) -> Option<SemanticSearchResult> {
        let cache = self.search_cache.lock().await;
        cache.get(cache_key).cloned()
    }

    async fn cache_result(&self, cache_key: &str, result: &SemanticSearchResult) {
        let mut cache = self.search_cache.lock().await;
        
        // Simple cache size management
        if cache.len() >= 100 {
            // Remove oldest entries (simplified)
            cache.clear();
        }
        
        cache.insert(cache_key.to_string(), result.clone());
    }

    // Metrics
    async fn record_successful_search(&self, search_time_ms: f64, results_count: usize) {
        let mut metrics = self.metrics.lock().await;
        metrics.total_searches += 1;
        metrics.successful_searches += 1;
        
        let total_searches = metrics.total_searches as f64;
        metrics.average_search_time_ms = 
            ((metrics.average_search_time_ms * (total_searches - 1.0)) + search_time_ms) / total_searches;
        
        metrics.average_results_per_search = 
            ((metrics.average_results_per_search * (total_searches - 1.0)) + results_count as f64) / total_searches;
    }

    async fn record_failed_search(&self) {
        let mut metrics = self.metrics.lock().await;
        metrics.total_searches += 1;
        metrics.failed_searches += 1;
    }

    async fn record_cache_hit(&self) {
        let mut metrics = self.metrics.lock().await;
        metrics.cache_hits += 1;
    }

    async fn record_cache_miss(&self) {
        let mut metrics = self.metrics.lock().await;
        metrics.cache_misses += 1;
    }

    pub async fn get_metrics(&self) -> SearchMetrics {
        let metrics = self.metrics.lock().await;
        SearchMetrics {
            total_searches: metrics.total_searches,
            successful_searches: metrics.successful_searches,
            failed_searches: metrics.failed_searches,
            average_search_time_ms: metrics.average_search_time_ms,
            average_results_per_search: metrics.average_results_per_search,
            cache_hits: metrics.cache_hits,
            cache_misses: metrics.cache_misses,
        }
    }

    pub async fn clear_cache(&self) {
        let mut cache = self.search_cache.lock().await;
        cache.clear();
        tracing::info!("Search cache cleared");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::*;

    #[tokio::test]
    async fn test_semantic_search_service_creation() {
        let embeddings_service = Arc::new(
            EnhancedEmbeddingsService::new().await.unwrap()
        );
        let search_service = SemanticSearchService::new(embeddings_service).await.unwrap();
        
        let metrics = search_service.get_metrics().await;
        assert_eq!(metrics.total_searches, 0);
        assert_eq!(metrics.successful_searches, 0);
        assert_eq!(metrics.failed_searches, 0);
    }

    #[tokio::test]
    async fn test_cache_key_generation() {
        let embeddings_service = Arc::new(
            EnhancedEmbeddingsService::new().await.unwrap()
        );
        let search_service = SemanticSearchService::new(embeddings_service).await.unwrap();

        let query1 = SemanticSearchQuery {
            query: "test function".to_string(),
            ..Default::default()
        };

        let query2 = SemanticSearchQuery {
            query: "test function".to_string(),
            ..Default::default()
        };

        let key1 = search_service.generate_cache_key(&query1);
        let key2 = search_service.generate_cache_key(&query2);

        assert_eq!(key1, key2);
    }

    #[test]
    fn test_cosine_similarity_calculation() {
        let embeddings_service = futures::executor::block_on(
            EnhancedEmbeddingsService::new()
        ).unwrap();
        let search_service = futures::executor::block_on(
            SemanticSearchService::new(Arc::new(embeddings_service))
        ).unwrap();

        let a = vec![1.0, 0.0, 0.0];
        let b = vec![1.0, 0.0, 0.0];
        assert_eq!(search_service.calculate_cosine_similarity(&a, &b), 1.0);

        let a = vec![1.0, 0.0, 0.0];
        let b = vec![0.0, 1.0, 0.0];
        assert_eq!(search_service.calculate_cosine_similarity(&a, &b), 0.0);

        let a = vec![1.0, 1.0, 0.0];
        let b = vec![1.0, 1.0, 0.0];
        assert_eq!(search_service.calculate_cosine_similarity(&a, &b), 1.0);
    }

    #[test]
    fn test_search_result_serialization() {
        let result = SearchResult {
            file_path: "test.rs".to_string(),
            language: "rust".to_string(),
            similarity_score: 0.95,
            relevance_score: 0.92,
            chunk_id: "chunk_123".to_string(),
            content_preview: "fn test() {}".to_string(),
            line_range: Some(LineRange { start_line: 1, end_line: 5 }),
            symbol_context: Some(SymbolContext {
                symbol_name: "test".to_string(),
                symbol_type: "function".to_string(),
                containing_function: None,
                containing_class: None,
                namespace: None,
            }),
            code_snippet: Some("fn test() {\n    // code\n}".to_string()),
            explanation: Some("This is a test function".to_string()),
            ranking_factors: RankingFactors {
                similarity_score: 0.95,
                language_match: 1.0,
                symbol_relevance: 0.8,
                code_quality: 0.85,
                recency: 1.0,
                file_importance: 0.7,
            },
        };

        let json = serde_json::to_string(&result).unwrap();
        let deserialized: SearchResult = serde_json::from_str(&json).unwrap();

        assert_eq!(result.file_path, deserialized.file_path);
        assert_eq!(result.similarity_score, deserialized.similarity_score);
        assert_eq!(result.relevance_score, deserialized.relevance_score);
    }

    #[test]
    fn test_search_query_defaults() {
        let query = SemanticSearchQuery::default();
        
        assert_eq!(query.max_results, 20);
        assert_eq!(query.similarity_threshold, 0.7);
        assert!(query.include_code_snippets);
        assert!(!query.include_explanations);
        assert!(matches!(query.search_strategy, SearchStrategy::SemanticSimilarity));
    }
}