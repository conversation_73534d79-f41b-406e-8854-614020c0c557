use anyhow::{Context, Result};
use crate::models::{FileAnalysis, DetectedPattern, PatternType, PatternLocation};
use crate::services::embeddings_enhancement::{EnhancedEmbeddingsService, FeatureToggles};
use serde::{Deserialize, Serialize};
use reqwest::Client;
use std::env;
use std::time::Duration;
use std::sync::Arc;
use tokio::sync::Mutex;
use chrono::{DateTime, Utc};
use uuid::Uuid;

const GEMINI_TIMEOUT: Duration = Duration::from_secs(45);
const MAX_RETRIES: u32 = 3;
const INITIAL_RETRY_DELAY: Duration = Duration::from_millis(1000);

#[derive(Debug, Serialize)]
struct GeminiRequest {
    contents: Vec<GeminiContent>,
    generation_config: GeminiGenerationConfig,
    safety_settings: Vec<GeminiSafetySettings>,
}

#[derive(Debug, Serial<PERSON>)]
struct GeminiContent {
    parts: Vec<GeminiPart>,
    role: String,
}

#[derive(Debug, Serialize)]
struct GeminiPart {
    text: String,
}

#[derive(Debug, Serialize)]
struct GeminiGenerationConfig {
    temperature: f32,
    top_p: f32,
    top_k: i32,
    max_output_tokens: i32,
    response_mime_type: String,
}

#[derive(Debug, Serialize)]
struct GeminiSafetySettings {
    category: String,
    threshold: String,
}

#[derive(Debug, Deserialize)]
struct GeminiResponse {
    candidates: Vec<GeminiCandidate>,
}

#[derive(Debug, Deserialize)]
struct GeminiCandidate {
    content: GeminiResponseContent,
}

#[derive(Debug, Deserialize)]
struct GeminiResponseContent {
    parts: Vec<GeminiResponsePart>,
}

#[derive(Debug, Deserialize)]
struct GeminiResponsePart {
    text: String,
}

#[derive(Debug, Deserialize)]
struct AIPatternAnalysis {
    patterns: Vec<AIDetectedPattern>,
    #[allow(dead_code)]
    confidence: f32,
    #[allow(dead_code)]
    reasoning: String,
}

#[derive(Debug, Deserialize)]
struct AIDetectedPattern {
    pattern_type: String,
    #[allow(dead_code)]
    pattern_name: String,
    confidence: f32,
    description: String,
    location: AIPatternLocation,
    suggestions: Vec<String>,
    #[allow(dead_code)]
    severity: String,
}

#[derive(Debug, Deserialize)]
struct AIPatternLocation {
    file_path: String,
    start_line: u32,
    end_line: u32,
    start_column: u32,
    end_column: u32,
}

// Circuit breaker states
#[derive(Debug, Clone)]
enum CircuitState {
    Closed,
    Open(DateTime<Utc>),
    HalfOpen,
}

#[derive(Debug, Default)]
pub struct AIPatternMetrics {
    total_analyses: u64,
    successful_analyses: u64,
    failed_analyses: u64,
    patterns_detected: u64,
    average_response_time_ms: f64,
    circuit_breaker_opens: u64,
    fallback_uses: u64,
    total_tokens_used: u64,
}

pub struct AIPatternDetector {
    client: Client,
    project_id: String,
    location: String,
    model_name: String,
    circuit_state: Arc<Mutex<CircuitState>>,
    failure_count: Arc<Mutex<u32>>,
    failure_threshold: u32,
    reset_timeout: Duration,
    feature_toggles: Arc<FeatureToggles>,
    metrics: Arc<Mutex<AIPatternMetrics>>,
    #[allow(dead_code)]
    embeddings_service: Arc<EnhancedEmbeddingsService>,
}

impl AIPatternDetector {
    pub async fn new(embeddings_service: Arc<EnhancedEmbeddingsService>) -> Result<Self> {
        let project_id = env::var("GCP_PROJECT_ID")
            .unwrap_or_else(|_| "vibe-match-463114".to_string());
        let location = env::var("GCP_REGION").unwrap_or_else(|_| "us-central1".to_string());
        let model_name = env::var("GEMINI_MODEL_NAME")
            .unwrap_or_else(|_| "gemini-2.5-flash".to_string());

        let client = Client::builder()
            .timeout(GEMINI_TIMEOUT)
            .build()
            .context("Failed to create HTTP client")?;

        let feature_toggles = embeddings_service.get_feature_toggles();

        Ok(Self {
            client,
            project_id,
            location,
            model_name,
            circuit_state: Arc::new(Mutex::new(CircuitState::Closed)),
            failure_count: Arc::new(Mutex::new(0)),
            failure_threshold: 5,
            reset_timeout: Duration::from_secs(60),
            feature_toggles,
            metrics: Arc::new(Mutex::new(AIPatternMetrics::default())),
            embeddings_service,
        })
    }

    pub async fn detect_ai_patterns(&self, analyses: &[FileAnalysis]) -> Result<Vec<DetectedPattern>> {
        if !self.feature_toggles.enable_ai_pattern_detection {
            tracing::info!("AI pattern detection disabled by feature toggle");
            return Ok(vec![]);
        }

        let mut all_patterns = Vec::new();
        
        // Process files in batches to avoid token limits
        const BATCH_SIZE: usize = 3;
        
        for (batch_idx, chunk) in analyses.chunks(BATCH_SIZE).enumerate() {
            // Check circuit breaker
            if !self.check_circuit_breaker().await? {
                tracing::warn!(
                    "Circuit breaker is open, skipping AI pattern detection for batch {}",
                    batch_idx
                );
                continue;
            }

            let start_time = std::time::Instant::now();
            match self.analyze_batch_with_ai(chunk).await {
                Ok(batch_patterns) => {
                    all_patterns.extend(batch_patterns);
                    self.record_success().await;
                    self.record_response_time(start_time.elapsed().as_millis() as f64).await;
                }
                Err(e) => {
                    tracing::error!(
                        "Failed to analyze batch {} with AI: {}",
                        batch_idx, e
                    );
                    self.record_failure().await;
                    
                    // Use fallback pattern detection
                    if let Ok(fallback_patterns) = self.fallback_pattern_detection(chunk).await {
                        all_patterns.extend(fallback_patterns);
                        self.record_fallback_use().await;
                    }
                }
            }
            
            // Add delay between batches to respect rate limits
            if batch_idx < analyses.chunks(BATCH_SIZE).len() - 1 {
                tokio::time::sleep(Duration::from_millis(1000)).await;
            }
        }

        Ok(all_patterns)
    }

    async fn analyze_batch_with_ai(&self, analyses: &[FileAnalysis]) -> Result<Vec<DetectedPattern>> {
        let prompt = self.build_analysis_prompt(analyses)?;
        
        let mut retry_delay = INITIAL_RETRY_DELAY;
        
        for attempt in 0..MAX_RETRIES {
            match self.call_gemini_api(&prompt).await {
                Ok(ai_analysis) => {
                    let patterns = self.convert_ai_patterns_to_detected_patterns(ai_analysis, analyses)?;
                    return Ok(patterns);
                }
                Err(e) => {
                    if !self.is_retryable_error(&e) || attempt == MAX_RETRIES - 1 {
                        return Err(e);
                    }
                    
                    tracing::warn!(
                        "AI analysis attempt {} failed, retrying in {:?}: {}",
                        attempt + 1,
                        retry_delay,
                        e
                    );
                    
                    tokio::time::sleep(retry_delay).await;
                    retry_delay = std::cmp::min(retry_delay * 2, Duration::from_secs(30));
                }
            }
        }
        
        Err(anyhow::anyhow!("All AI analysis attempts exhausted"))
    }

    fn build_analysis_prompt(&self, analyses: &[FileAnalysis]) -> Result<String> {
        let mut prompt = String::new();
        
        prompt.push_str("You are an expert code analyst. Analyze the following code files for patterns, anti-patterns, code smells, security issues, and performance problems. Return a JSON response with the following structure:\n\n");
        
        prompt.push_str("{\n");
        prompt.push_str("  \"patterns\": [\n");
        prompt.push_str("    {\n");
        prompt.push_str("      \"pattern_type\": \"design_pattern | anti_pattern | code_smell | security_issue | performance_issue\",\n");
        prompt.push_str("      \"pattern_name\": \"Specific pattern name\",\n");
        prompt.push_str("      \"confidence\": 0.95,\n");
        prompt.push_str("      \"description\": \"Clear description of the pattern\",\n");
        prompt.push_str("      \"location\": {\n");
        prompt.push_str("        \"file_path\": \"path/to/file.rs\",\n");
        prompt.push_str("        \"start_line\": 10,\n");
        prompt.push_str("        \"end_line\": 20,\n");
        prompt.push_str("        \"start_column\": 4,\n");
        prompt.push_str("        \"end_column\": 15\n");
        prompt.push_str("      },\n");
        prompt.push_str("      \"suggestions\": [\"Specific improvement suggestions\"],\n");
        prompt.push_str("      \"severity\": \"low | medium | high | critical\"\n");
        prompt.push_str("    }\n");
        prompt.push_str("  ],\n");
        prompt.push_str("  \"confidence\": 0.85,\n");
        prompt.push_str("  \"reasoning\": \"Explain your analysis process\"\n");
        prompt.push_str("}\n\n");
        
        prompt.push_str("Focus on:\n");
        prompt.push_str("1. Design patterns (Singleton, Factory, Observer, etc.)\n");
        prompt.push_str("2. Anti-patterns (God object, Spaghetti code, etc.)\n");
        prompt.push_str("3. Code smells (Long methods, duplicate code, etc.)\n");
        prompt.push_str("4. Security vulnerabilities (SQL injection, XSS, etc.)\n");
        prompt.push_str("5. Performance issues (N+1 queries, inefficient algorithms, etc.)\n\n");
        
        prompt.push_str("Code files to analyze:\n\n");
        
        for analysis in analyses {
            prompt.push_str(&format!("File: {}\n", analysis.path));
            prompt.push_str(&format!("Language: {}\n", analysis.language));
            prompt.push_str(&format!("Lines of code: {}\n", analysis.metrics.lines_of_code));
            prompt.push_str(&format!("Complexity: {}\n", analysis.metrics.complexity));
            
            if let Some(symbols) = &analysis.symbols {
                prompt.push_str("Symbols: ");
                for symbol in symbols.iter().take(5) {
                    prompt.push_str(&format!("{} ", symbol.name));
                }
                prompt.push_str("\n");
            }
            
            if let Some(text) = &analysis.ast.text {
                let preview = text.chars().take(2000).collect::<String>();
                prompt.push_str(&format!("Code:\n```\n{}\n```\n\n", preview));
            }
        }
        
        Ok(prompt)
    }

    async fn call_gemini_api(&self, prompt: &str) -> Result<AIPatternAnalysis> {
        let auth_token = self.get_auth_token().await?;
        
        let endpoint = format!(
            "https://{}-aiplatform.googleapis.com/v1/projects/{}/locations/{}/publishers/google/models/{}:generateContent",
            self.location, self.project_id, self.location, self.model_name
        );

        let request = GeminiRequest {
            contents: vec![GeminiContent {
                parts: vec![GeminiPart {
                    text: prompt.to_string(),
                }],
                role: "user".to_string(),
            }],
            generation_config: GeminiGenerationConfig {
                temperature: 0.1,
                top_p: 0.8,
                top_k: 40,
                max_output_tokens: 8192,
                response_mime_type: "application/json".to_string(),
            },
            safety_settings: vec![
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_HARASSMENT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_HATE_SPEECH".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_SEXUALLY_EXPLICIT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
                GeminiSafetySettings {
                    category: "HARM_CATEGORY_DANGEROUS_CONTENT".to_string(),
                    threshold: "BLOCK_MEDIUM_AND_ABOVE".to_string(),
                },
            ],
        };

        let response = self.client
            .post(&endpoint)
            .bearer_auth(&auth_token)
            .json(&request)
            .send()
            .await
            .context("Failed to send request to Gemini API")?;

        let status = response.status();
        
        if !status.is_success() {
            let error_body = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow::anyhow!(
                "Gemini API request failed with status {}: {}",
                status,
                error_body
            ));
        }

        let gemini_response: GeminiResponse = response
            .json()
            .await
            .context("Failed to parse Gemini response")?;

        if gemini_response.candidates.is_empty() {
            return Err(anyhow::anyhow!("No candidates in Gemini response"));
        }

        let response_text = &gemini_response.candidates[0].content.parts[0].text;
        
        let ai_analysis: AIPatternAnalysis = serde_json::from_str(response_text)
            .context("Failed to parse AI analysis JSON")?;

        Ok(ai_analysis)
    }

    fn convert_ai_patterns_to_detected_patterns(
        &self,
        ai_analysis: AIPatternAnalysis,
        analyses: &[FileAnalysis],
    ) -> Result<Vec<DetectedPattern>> {
        let mut patterns = Vec::new();
        
        for ai_pattern in ai_analysis.patterns {
            let pattern_type = match ai_pattern.pattern_type.as_str() {
                "design_pattern" => PatternType::DesignPattern,
                "anti_pattern" => PatternType::AntiPattern,
                "code_smell" => PatternType::CodeSmell,
                "security_issue" => PatternType::SecurityIssue,
                "performance_issue" => PatternType::PerformanceIssue,
                _ => PatternType::CodeSmell, // Default fallback
            };
            
            // Find the corresponding analysis to get the correct range
            let file_analysis = analyses.iter()
                .find(|a| a.path == ai_pattern.location.file_path)
                .cloned();
            
            let range = if let Some(_analysis) = file_analysis {
                crate::models::Range {
                    start: crate::models::Position {
                        line: ai_pattern.location.start_line,
                        column: ai_pattern.location.start_column,
                        byte: 0, // We don't have byte info from AI
                    },
                    end: crate::models::Position {
                        line: ai_pattern.location.end_line,
                        column: ai_pattern.location.end_column,
                        byte: 0, // We don't have byte info from AI
                    },
                }
            } else {
                // Fallback range if file not found
                crate::models::Range {
                    start: crate::models::Position { line: 0, column: 0, byte: 0 },
                    end: crate::models::Position { line: 0, column: 0, byte: 0 },
                }
            };
            
            let mut description = ai_pattern.description.clone();
            if !ai_pattern.suggestions.is_empty() {
                description.push_str(&format!("\n\nSuggestions:\n{}", ai_pattern.suggestions.join("\n")));
            }
            
            patterns.push(DetectedPattern {
                pattern_id: format!("ai_pattern_{}", Uuid::new_v4()),
                pattern_type,
                confidence: ai_pattern.confidence as f64,
                location: PatternLocation {
                    file_path: ai_pattern.location.file_path,
                    range,
                },
                description: Some(description),
            });
        }
        
        Ok(patterns)
    }

    async fn fallback_pattern_detection(&self, analyses: &[FileAnalysis]) -> Result<Vec<DetectedPattern>> {
        // Use the traditional pattern detector as fallback
        use crate::services::pattern_detector::PatternDetector;
        
        let detector = PatternDetector::new();
        let mut patterns = Vec::new();
        
        for analysis in analyses {
            let detected_patterns = detector.detect_patterns(analysis);
            patterns.extend(detected_patterns);
        }
        
        Ok(patterns)
    }

    async fn get_auth_token(&self) -> Result<String> {
        // Check if we're running on Cloud Run or GCE (has metadata server)
        if env::var("K_SERVICE").is_ok() || env::var("GAE_ENV").is_ok() {
            // Running on Cloud Run or App Engine - use metadata server
            let metadata_url = format!(
                "http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token?scopes={}",
                "https://www.googleapis.com/auth/cloud-platform"
            );
            
            let response = self.client
                .get(&metadata_url)
                .header("Metadata-Flavor", "Google")
                .send()
                .await
                .context("Failed to fetch token from metadata server")?;
                
            if !response.status().is_success() {
                return Err(anyhow::anyhow!(
                    "Metadata server returned error: {}",
                    response.status()
                ));
            }
            
            #[derive(Deserialize)]
            struct TokenResponse {
                access_token: String,
            }
            
            let token_response: TokenResponse = response
                .json()
                .await
                .context("Failed to parse token response")?;
                
            Ok(token_response.access_token)
        } else if let Ok(_creds_path) = env::var("GOOGLE_APPLICATION_CREDENTIALS") {
            // Local development with service account key file
            use std::process::Command;
            
            let output = Command::new("gcloud")
                .args(&["auth", "application-default", "print-access-token"])
                .output()
                .context("Failed to run gcloud command")?;
                
            if !output.status.success() {
                return Err(anyhow::anyhow!(
                    "gcloud command failed: {}",
                    String::from_utf8_lossy(&output.stderr)
                ));
            }
            
            let token = String::from_utf8(output.stdout)
                .context("Invalid UTF-8 in token")?
                .trim()
                .to_string();
                
            Ok(token)
        } else {
            // No authentication available
            Err(anyhow::anyhow!(
                "No authentication method available. Set GOOGLE_APPLICATION_CREDENTIALS or run on Cloud Run"
            ))
        }
    }

    // Circuit breaker implementation
    async fn check_circuit_breaker(&self) -> Result<bool> {
        let mut state = self.circuit_state.lock().await;
        
        match *state {
            CircuitState::Closed => Ok(true),
            CircuitState::Open(reset_time) => {
                if Utc::now() > reset_time {
                    *state = CircuitState::HalfOpen;
                    tracing::info!("AI pattern detection circuit breaker transitioning to half-open");
                    Ok(true)
                } else {
                    Ok(false)
                }
            }
            CircuitState::HalfOpen => Ok(true),
        }
    }

    async fn record_success(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;
        
        *failures = 0;
        metrics.successful_analyses += 1;
        
        if matches!(*state, CircuitState::HalfOpen) {
            *state = CircuitState::Closed;
            tracing::info!("AI pattern detection circuit breaker closed after successful request");
        }
    }

    async fn record_failure(&self) {
        let mut state = self.circuit_state.lock().await;
        let mut failures = self.failure_count.lock().await;
        let mut metrics = self.metrics.lock().await;
        
        *failures += 1;
        metrics.failed_analyses += 1;
        
        if *failures >= self.failure_threshold {
            let reset_time = Utc::now() + chrono::Duration::from_std(self.reset_timeout)
                .unwrap_or_else(|_| chrono::Duration::seconds(300));
            *state = CircuitState::Open(reset_time);
            *failures = 0;
            metrics.circuit_breaker_opens += 1;
            
            tracing::error!(
                "AI pattern detection circuit breaker opened after {} failures, will reset at {}",
                self.failure_threshold,
                reset_time
            );
        }
    }

    async fn record_fallback_use(&self) {
        let mut metrics = self.metrics.lock().await;
        metrics.fallback_uses += 1;
    }

    async fn record_response_time(&self, response_time_ms: f64) {
        let mut metrics = self.metrics.lock().await;
        metrics.total_analyses += 1;
        
        // Calculate running average
        let total_analyses = metrics.total_analyses as f64;
        metrics.average_response_time_ms = 
            ((metrics.average_response_time_ms * (total_analyses - 1.0)) + response_time_ms) / total_analyses;
    }

    fn is_retryable_error(&self, error: &anyhow::Error) -> bool {
        let error_str = error.to_string().to_lowercase();
        
        error_str.contains("timeout") ||
        error_str.contains("temporarily unavailable") ||
        error_str.contains("429") || // Rate limit
        error_str.contains("500") || // Internal server error
        error_str.contains("502") || // Bad gateway
        error_str.contains("503") || // Service unavailable
        error_str.contains("504")    // Gateway timeout
    }

    pub async fn get_metrics(&self) -> AIPatternMetrics {
        let metrics = self.metrics.lock().await;
        AIPatternMetrics {
            total_analyses: metrics.total_analyses,
            successful_analyses: metrics.successful_analyses,
            failed_analyses: metrics.failed_analyses,
            patterns_detected: metrics.patterns_detected,
            average_response_time_ms: metrics.average_response_time_ms,
            circuit_breaker_opens: metrics.circuit_breaker_opens,
            fallback_uses: metrics.fallback_uses,
            total_tokens_used: metrics.total_tokens_used,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::models::*;

    #[tokio::test]
    async fn test_build_analysis_prompt() {
        let embeddings_service = Arc::new(
            EnhancedEmbeddingsService::new().await.unwrap()
        );
        let detector = AIPatternDetector::new(embeddings_service).await.unwrap();
        
        let analysis = FileAnalysis {
            path: "test.rs".to_string(),
            language: "rust".to_string(),
            content_hash: "hash123".to_string(),
            size_bytes: Some(1000),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position { line: 0, column: 0, byte: 0 },
                    end: Position { line: 10, column: 0, byte: 100 },
                },
                children: vec![],
                properties: None,
                text: Some("fn main() { println!(\"Hello\"); }".to_string()),
            },
            metrics: FileMetrics {
                lines_of_code: 10,
                total_lines: Some(15),
                complexity: 1,
                maintainability_index: 80.0,
                function_count: 1,
                class_count: 0,
                comment_ratio: 0.1,
            },
            chunks: None,
            symbols: Some(vec![
                Symbol {
                    name: "main".to_string(),
                    symbol_type: SymbolType::Function,
                    range: Range {
                        start: Position { line: 0, column: 0, byte: 0 },
                        end: Position { line: 2, column: 0, byte: 50 },
                    },
                    visibility: Some(SymbolVisibility::Public),
                    signature: Some("fn main()".to_string()),
                    documentation: None,
                }
            ]),
        };
        
        let prompt = detector.build_analysis_prompt(&[analysis]).unwrap();
        
        assert!(prompt.contains("code analyst"));
        assert!(prompt.contains("test.rs"));
        assert!(prompt.contains("rust"));
        assert!(prompt.contains("fn main()"));
    }

    #[test]
    fn test_convert_ai_patterns() {
        let ai_analysis = AIPatternAnalysis {
            patterns: vec![
                AIDetectedPattern {
                    pattern_type: "design_pattern".to_string(),
                    pattern_name: "Singleton".to_string(),
                    confidence: 0.95,
                    description: "Singleton pattern detected".to_string(),
                    location: AIPatternLocation {
                        file_path: "test.rs".to_string(),
                        start_line: 5,
                        end_line: 15,
                        start_column: 0,
                        end_column: 10,
                    },
                    suggestions: vec!["Consider using dependency injection".to_string()],
                    severity: "medium".to_string(),
                }
            ],
            confidence: 0.85,
            reasoning: "Analysis based on code structure".to_string(),
        };
        
        let analysis = FileAnalysis {
            path: "test.rs".to_string(),
            language: "rust".to_string(),
            content_hash: "hash123".to_string(),
            size_bytes: Some(1000),
            ast: AstNode {
                node_type: "root".to_string(),
                name: None,
                range: Range {
                    start: Position { line: 0, column: 0, byte: 0 },
                    end: Position { line: 10, column: 0, byte: 100 },
                },
                children: vec![],
                properties: None,
                text: None,
            },
            metrics: FileMetrics::default(),
            chunks: None,
            symbols: None,
        };
        
        let embeddings_service = std::sync::Arc::new(
            futures::executor::block_on(EnhancedEmbeddingsService::new()).unwrap()
        );
        let detector = futures::executor::block_on(AIPatternDetector::new(embeddings_service)).unwrap();
        
        let patterns = detector.convert_ai_patterns_to_detected_patterns(ai_analysis, &[analysis]).unwrap();
        
        assert_eq!(patterns.len(), 1);
        assert!(matches!(patterns[0].pattern_type, PatternType::DesignPattern));
        assert_eq!(patterns[0].confidence, 0.95);
        assert!(patterns[0].description.is_some());
        assert!(patterns[0].description.as_ref().unwrap().contains("Singleton"));
        assert!(patterns[0].description.as_ref().unwrap().contains("dependency injection"));
    }
}