use crate::models::{<PERSON>t<PERSON><PERSON>, Code<PERSON>hunk, <PERSON><PERSON><PERSON><PERSON>, ChunkContext};
use std::sync::atomic::{AtomicUsize, Ordering};

/// Extracts meaningful code chunks from AST nodes
pub struct ChunkExtractor {
    max_chunk_size: usize,
    chunk_counter: AtomicUsize,
}

impl ChunkExtractor {
    /// Create a new chunk extractor
    pub fn new() -> Self {
        Self {
            max_chunk_size: 8192, // From contract
            chunk_counter: AtomicUsize::new(0),
        }
    }

    /// Create with custom max chunk size
    pub fn with_max_size(max_chunk_size: usize) -> Self {
        Self {
            max_chunk_size,
            chunk_counter: AtomicUsize::new(0),
        }
    }

    /// Extract code chunks from an AST
    pub fn extract(&self, root: &AstNode, source: &str, language: &str) -> Vec<CodeChunk> {
        let mut chunks = Vec::new();
        self.extract_recursive(root, source, language, &mut chunks);
        chunks
    }

    /// Recursively extract chunks from AST nodes
    fn extract_recursive(&self, node: &AstNode, source: &str, language: &str, chunks: &mut Vec<CodeChunk>) {
        // Determine if this node should be a chunk
        if let Some(chunk_type) = self.get_chunk_type(&node.node_type) {
            if let Some(chunk) = self.create_chunk(node, source, language, chunk_type) {
                chunks.push(chunk);
            }
        }

        // Recurse into children
        for child in &node.children {
            self.extract_recursive(child, source, language, chunks);
        }
    }

    /// Determine the chunk type for a node
    fn get_chunk_type(&self, node_type: &str) -> Option<ChunkType> {
        match node_type {
            // Functions
            "function_item" | "function_declaration" | "function_definition" |
            "method_definition" | "arrow_function" | "function" => Some(ChunkType::Function),
            
            // Classes
            "struct_item" | "class_declaration" | "class_definition" |
            "struct_declaration" | "enum_declaration" | "interface_declaration" => Some(ChunkType::Class),
            
            // Methods
            "impl_item" | "method_declaration" | "constructor_declaration" => Some(ChunkType::Method),
            
            // Blocks
            "block_statement" | "block" | "compound_statement" => Some(ChunkType::Block),
            
            // Comments
            "comment" | "line_comment" | "block_comment" | "doc_comment" => Some(ChunkType::Comment),
            
            // Imports
            "use_declaration" | "import_statement" | "import_declaration" |
            "require_statement" | "include_statement" => Some(ChunkType::Import),
            
            _ => None,
        }
    }

    /// Create a code chunk from a node
    fn create_chunk(&self, node: &AstNode, source: &str, language: &str, chunk_type: ChunkType) -> Option<CodeChunk> {
        // Get the text content
        let text = if let Some(ref node_text) = node.text {
            node_text.clone()
        } else {
            // Extract from source using byte positions
            self.extract_text_from_source(node, source)?
        };

        // Skip empty or too large chunks
        if text.is_empty() || text.len() > self.max_chunk_size {
            return None;
        }

        // Generate unique chunk ID
        let counter = self.chunk_counter.fetch_add(1, Ordering::Relaxed);
        let chunk_id = format!("chunk_{:016x}", counter);

        Some(CodeChunk {
            chunk_id,
            content: text,
            range: node.range.clone(),
            chunk_type,
            language: Some(language.to_string()),
            context: self.extract_context(node),
        })
    }

    /// Extract text from source using node range
    fn extract_text_from_source(&self, node: &AstNode, source: &str) -> Option<String> {
        let start_byte = node.range.start.byte as usize;
        let end_byte = node.range.end.byte as usize;

        // Validate byte positions
        if start_byte >= source.len() || end_byte > source.len() || start_byte >= end_byte {
            return None;
        }

        Some(source[start_byte..end_byte].to_string())
    }

    /// Extract context information for a chunk
    fn extract_context(&self, node: &AstNode) -> Option<ChunkContext> {
        // Build context from node name and type
        let parent_symbol = if let Some(ref name) = node.name {
            Some(format!("{} {}", node.node_type, name))
        } else {
            Some(node.node_type.clone())
        };

        // Extract imports and dependencies from the AST context
        let imports = self.extract_imports_from_context(node);
        let dependencies = self.extract_dependencies_from_context(node);

        Some(ChunkContext {
            parent_symbol,
            imports: if imports.is_empty() { None } else { Some(imports) },
            dependencies: if dependencies.is_empty() { None } else { Some(dependencies) },
        })
    }

    /// Extract import statements from the AST context
    fn extract_imports_from_context(&self, node: &AstNode) -> Vec<String> {
        let mut imports = Vec::new();
        
        // Recursively search for import-related nodes
        self.collect_imports_recursive(node, &mut imports);
        
        // Deduplicate imports
        imports.sort();
        imports.dedup();
        imports
    }

    /// Recursively collect import statements from AST nodes
    fn collect_imports_recursive(&self, node: &AstNode, imports: &mut Vec<String>) {
        // Check if this node represents an import statement
        match node.node_type.as_str() {
            // Rust imports
            "use_declaration" => {
                if let Some(import_name) = self.extract_rust_import(node) {
                    imports.push(import_name);
                }
            }
            // JavaScript/TypeScript imports
            "import_statement" => {
                if let Some(import_name) = self.extract_js_import(node) {
                    imports.push(import_name);
                }
            }
            // Python imports
            "import_from_statement" => {
                if let Some(import_name) = self.extract_python_import(node) {
                    imports.push(import_name);
                }
            }
            // Java/Go imports (shared declaration type)
            "import_declaration" => {
                // Try both Java and Go extraction
                if let Some(import_name) = self.extract_java_import(node) {
                    imports.push(import_name);
                } else if let Some(import_name) = self.extract_go_import(node) {
                    imports.push(import_name);
                }
            }
            // Go imports
            "import_spec" => {
                if let Some(import_name) = self.extract_go_import(node) {
                    imports.push(import_name);
                }
            }
            // C/C++ includes
            "preproc_include" => {
                if let Some(include_name) = self.extract_c_include(node) {
                    imports.push(include_name);
                }
            }
            _ => {}
        }

        // Recursively search children
        for child in &node.children {
            self.collect_imports_recursive(child, imports);
        }
    }

    /// Extract dependencies from the AST context (function calls, class references, etc.)
    fn extract_dependencies_from_context(&self, node: &AstNode) -> Vec<String> {
        let mut dependencies = Vec::new();
        
        // Recursively search for dependency-related nodes
        self.collect_dependencies_recursive(node, &mut dependencies);
        
        // Deduplicate dependencies
        dependencies.sort();
        dependencies.dedup();
        dependencies
    }

    /// Recursively collect dependencies from AST nodes
    fn collect_dependencies_recursive(&self, node: &AstNode, dependencies: &mut Vec<String>) {
        // Check if this node represents a dependency reference
        match node.node_type.as_str() {
            // Function calls
            "call_expression" | "function_call" | "method_invocation" => {
                if let Some(func_name) = self.extract_function_call(node) {
                    dependencies.push(func_name);
                }
            }
            // Type references
            "type_identifier" | "class_name" | "struct_name" => {
                if let Some(ref name) = node.name {
                    dependencies.push(name.clone());
                } else if let Some(ref text) = node.text {
                    dependencies.push(text.clone());
                }
            }
            // Variable references (only significant ones)
            "identifier" => {
                if let Some(identifier) = self.extract_significant_identifier(node) {
                    dependencies.push(identifier);
                }
            }
            _ => {}
        }

        // Recursively search children
        for child in &node.children {
            self.collect_dependencies_recursive(child, dependencies);
        }
    }

    // Language-specific import extractors
    fn extract_rust_import(&self, node: &AstNode) -> Option<String> {
        // Extract from Rust use declarations like "use std::collections::HashMap"
        node.text.clone().or_else(|| {
            // Try to build from children
            self.build_import_path_from_children(node)
        })
    }

    fn extract_js_import(&self, node: &AstNode) -> Option<String> {
        // Extract from JS/TS import statements like "import React from 'react'"
        node.text.clone()
    }

    fn extract_python_import(&self, node: &AstNode) -> Option<String> {
        // Extract from Python imports like "import os" or "from collections import defaultdict"
        node.text.clone()
    }

    fn extract_java_import(&self, node: &AstNode) -> Option<String> {
        // Extract from Java imports like "import java.util.List"
        node.text.clone()
    }

    fn extract_go_import(&self, node: &AstNode) -> Option<String> {
        // Extract from Go imports like `import "fmt"`
        node.text.clone()
    }

    fn extract_c_include(&self, node: &AstNode) -> Option<String> {
        // Extract from C/C++ includes like "#include <stdio.h>"
        node.text.clone()
    }

    fn extract_function_call(&self, node: &AstNode) -> Option<String> {
        // Extract function name from call expressions
        if let Some(ref name) = node.name {
            Some(name.clone())
        } else {
            // Look for function name in children
            for child in &node.children {
                if child.node_type == "identifier" || child.node_type == "field_identifier" {
                    if let Some(func_name) = child.name.as_ref().or(child.text.as_ref()) {
                        return Some(func_name.clone());
                    }
                }
            }
            None
        }
    }

    fn extract_significant_identifier(&self, node: &AstNode) -> Option<String> {
        // Only collect identifiers that seem significant (not single letters, common keywords)
        if let Some(identifier) = node.name.as_ref().or(node.text.as_ref()) {
            // Skip common single-letter variables and keywords
            if identifier.len() > 1 
                && !self.is_common_keyword(identifier) 
                && !identifier.chars().all(|c| c.is_ascii_lowercase() && identifier.len() == 1) {
                Some(identifier.clone())
            } else {
                None
            }
        } else {
            None
        }
    }

    fn build_import_path_from_children(&self, node: &AstNode) -> Option<String> {
        // Build import path by concatenating identifier children
        let mut path_parts = Vec::new();
        
        for child in &node.children {
            if child.node_type == "identifier" || child.node_type == "scoped_identifier" {
                if let Some(part) = child.name.as_ref().or(child.text.as_ref()) {
                    path_parts.push(part.clone());
                }
            }
        }
        
        if path_parts.is_empty() {
            None
        } else {
            Some(path_parts.join("::"))
        }
    }

    fn is_common_keyword(&self, identifier: &str) -> bool {
        // List of common keywords to exclude from dependencies
        matches!(identifier, 
            "if" | "else" | "for" | "while" | "do" | "switch" | "case" | "break" | "continue" |
            "function" | "var" | "let" | "const" | "return" | "new" | "this" | "super" |
            "class" | "extends" | "implements" | "interface" | "enum" | "struct" |
            "public" | "private" | "protected" | "static" | "final" | "abstract" |
            "try" | "catch" | "finally" | "throw" | "throws" |
            "true" | "false" | "null" | "undefined" | "None" | "self" |
            "fn" | "impl" | "trait" | "mod" | "use" | "pub" | "match" | "loop" |
            "def" | "class" | "import" | "from" | "as" | "with" | "pass" | "yield" |
            "async" | "await" | "lambda" | "global" | "nonlocal" |
            "void" | "int" | "char" | "float" | "double" | "bool" | "string" |
            "package" | "import" | "goto" | "sizeof" | "typedef"
        )
    }

    /// Reset the chunk counter
    pub fn reset_counter(&self) {
        self.chunk_counter.store(0, Ordering::Relaxed);
    }

    /// Get current counter value
    pub fn get_counter(&self) -> usize {
        self.chunk_counter.load(Ordering::Relaxed)
    }
}

impl Default for ChunkExtractor {
    fn default() -> Self {
        Self::new()
    }
}