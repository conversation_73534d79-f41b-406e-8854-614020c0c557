// services/analysis-engine/src/parser/language_registry.rs

use std::collections::HashMap;
use once_cell::sync::Lazy;

// Include the bindings generated by the build.rs script.
// This file contains the `extern "C"` block and the `get_language` function.
include!(concat!(env!("OUT_DIR"), "/language_bindings.rs"));

/// Type-safe wrapper for tree-sitter languages
#[derive(Clone)]
pub struct TreeSitterLanguage(pub Language);

impl TreeSitterLanguage {
    /// Create a new language wrapper
    pub fn new(lang: Language) -> Self {
        TreeSitterLanguage(lang)
    }

    /// Get the inner language
    pub fn inner(&self) -> &Language {
        &self.0
    }
}

// The LANGUAGE_REGISTRY is kept for potential future use cases where a language
// might need to be registered manually, but it is no longer the primary mechanism.
pub static LANGUAGE_REGISTRY: Lazy<HashMap<&'static str, TreeSitterLanguage>> = Lazy::new(|| {
    HashMap::new()
});

// The primary `get_language` function is now the one generated by our build script.
// We re-export it here for a consistent module interface.
// pub use self::get_language;

/// Check if a language is supported by checking if our generated function can find it.
pub fn is_language_supported(name: &str) -> bool {
    get_language(name).is_some()
}

/// Get all supported language names.
pub fn supported_languages() -> &'static [&'static str] {
    SUPPORTED_LANGUAGES
}

/// Languages that require custom parsers (not tree-sitter)
pub fn requires_custom_parser(name: &str) -> bool {
    matches!(name, "sql" | "toml" | "xml" | "markdown")
}

/// Languages that are known but not yet supported
pub fn is_known_unsupported(name: &str) -> bool {
    false
}