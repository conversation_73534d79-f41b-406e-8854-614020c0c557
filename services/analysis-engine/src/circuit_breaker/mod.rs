//! Circuit breaker for external service calls
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use std::time::Duration;

use circuit_breaker::{CircuitBreaker, Error as CircuitBreakerError, State};

use crate::config::CircuitBreakerConfig;

/// Manages circuit breakers for all external services
pub struct CircuitBreakerManager {
    breakers: Mutex<HashMap<String, Arc<Mutex<CircuitBreaker>>>>,
    config: CircuitBreakerConfig,
}

impl CircuitBreakerManager {
    /// Create a new circuit breaker manager
    pub fn new(config: CircuitBreakerConfig) -> Self {
        Self {
            breakers: Mutex::new(HashMap::new()),
            config,
        }
    }

    /// Get a circuit breaker for a service, creating one if it doesn't exist
    pub fn get_breaker(&self, service_name: &str) -> Arc<Mutex<CircuitBreaker>> {
        let mut breakers = self.breakers.lock().unwrap();
        breakers
            .entry(service_name.to_string())
            .or_insert_with(|| {
                let breaker = CircuitBreaker::new(
                    self.config.failure_threshold.into(),
                    Duration::from_secs(self.config.reset_timeout_secs),
                );
                Arc::new(Mutex::new(breaker))
            })
            .clone()
    }

    /// Execute a function protected by a circuit breaker
    pub async fn call<F, T, E>(&self, service_name: &str, operation: F) -> Result<T, CircuitBreakerError>
    where
        F: Fn() -> Result<T, E>,
        E: std::error::Error + Send + Sync + 'static,
    {
        let breaker = self.get_breaker(service_name);
        let mut breaker_guard = breaker.lock().unwrap();
        breaker_guard.call(operation).map_err(|e| e.into())
    }

    /// Get the status of a circuit breaker
    pub fn get_status(&self, service_name: &str) -> Option<State> {
        let breakers = self.breakers.lock().unwrap();
        breakers
            .get(service_name)
            .map(|breaker| breaker.lock().unwrap().state())
    }
}