# 🔧 Analysis Engine Operations Guide

This comprehensive guide covers deployment, database migrations, monitoring, and operational procedures for the Analysis Engine service.

## 📑 Table of Contents

1. [Deployment](#deployment)
2. [Database & Migrations](#database--migrations)
3. [Monitoring & Observability](#monitoring--observability)
4. [GCP Permissions & IAM](#gcp-permissions--iam)
5. [Troubleshooting](#troubleshooting)

---

## 🚀 Deployment

### Overview

The Analysis Engine supports multiple deployment strategies:
- **Standard deployment** for development/testing
- **Cloud Run deployment** for production
- **Canary deployments** with gradual rollout
- **Blue-green deployment** options

### Docker Build & Deploy

#### Standard Build
```bash
# Build for standard deployment (with shell access)
docker build -t analysis-engine:latest .

# Run locally
docker run -d \
  --name analysis-engine \
  -p 8001:8001 \
  -e GCP_PROJECT_ID="vibe-match-463114" \
  -e GCP_REGION="us-central1" \
  analysis-engine:latest
```

#### Cloud Run Build
```bash
# Build for Cloud Run (distroless, minimal attack surface)
docker build -t analysis-engine:cloudrun \
  --build-arg RUNTIME_TARGET=distroless .

# Deploy to Cloud Run
gcloud run deploy analysis-engine \
  --image gcr.io/vibe-match-463114/analysis-engine:cloudrun \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --cpu-boost \
  --memory 4Gi \
  --max-instances 1000 \
  --min-instances 0 \
  --service-account <EMAIL>
```

### Deployment Scripts

```bash
# One-time setup (IAM permissions)
./scripts/setup/grant_iam_permissions.sh

# Deploy to production
./scripts/deployment/deploy-optimized.sh
```

### Environment Variables

```bash
# Required
export GCP_PROJECT_ID="vibe-match-463114"
export GCP_REGION="us-central1"
export SPANNER_INSTANCE_ID="ccl-production"
export SPANNER_DATABASE_ID="ccl-main"
export GEMINI_MODEL_NAME="gemini-2.5-flash"

# Performance Tuning
export MAX_CONCURRENT_ANALYSES=50
export MEMORY_LIMIT_MB=4096
export CACHE_SIZE_MB=1024
export ENABLE_STREAMING=true

# Security Features
export ENABLE_VULNERABILITY_DETECTION=true
export ENABLE_SECRETS_DETECTION=true
export ENABLE_COMPLIANCE_CHECKING=true
```

---

## 🗄️ Database & Migrations

### Spanner Configuration

- **Instance**: ccl-production
- **Database**: ccl-main
- **Region**: us-central1

### Running Migrations

```bash
# Run all pending migrations
./scripts/database/run_migrations.sh

# Rollback last migration
./scripts/database/rollback_migration.sh

# Check migration status
cargo run --bin check_migrations
```

### Migration Files

Located in `/migrations/`:
- `000_create_base_schema.sql` - Base tables
- `001_add_analysis_metadata.sql` - Analysis metadata
- `002_create_file_analyses_table.sql` - File analysis storage
- `003_add_missing_indexes.sql` - Performance indexes
- `004_create_security_intelligence_schema.sql` - Security features

### Best Practices

1. **Always backup before migrations**
2. **Test migrations in staging first**
3. **Use transactions for data integrity**
4. **Monitor performance after index changes**

---

## 📊 Monitoring & Observability

### Health Check Endpoints

```rust
GET /health           // Basic service health
GET /ready           // Kubernetes readiness probe
GET /metrics         // Prometheus metrics
GET /api/v1/status   // Detailed system status
```

### Key Metrics

#### Application Metrics
- `analysis_requests_total` - Total analysis requests
- `analysis_duration_seconds` - Analysis duration histogram
- `parser_language_usage` - Language parsing statistics
- `ai_service_calls_total` - AI service usage
- `security_vulnerabilities_detected` - Security findings

#### System Metrics
- `memory_usage_bytes` - Memory consumption
- `cpu_usage_percent` - CPU utilization
- `active_connections` - Database connections
- `cache_hit_ratio` - Cache effectiveness

### Alerting Rules

```yaml
Critical Alerts:
- Service Down: health endpoint fails
- High Error Rate: >5% errors
- Memory Pressure: >80% usage
- Response Time: p95 >30s

Warning Alerts:
- Elevated Error Rate: >2% errors
- Cache Miss Rate: >50%
- Queue Backup: >1000 pending
```

### Grafana Dashboards

1. **Service Overview** - Key metrics and SLIs
2. **Performance Analysis** - Latency and throughput
3. **Security Intelligence** - Vulnerability trends
4. **Resource Usage** - CPU, memory, connections
5. **Business Metrics** - Usage by language/feature

### Logging

Structured logging with correlation IDs:
```rust
info!(
    request_id = %request_id,
    language = %language,
    file_size = file_size,
    "Starting file analysis"
);
```

---

## 🔐 GCP Permissions & IAM

### Current Status
- **Project**: vibe-match-463114
- **Service Account**: <EMAIL>
- **Required Role**: `roles/aiplatform.user` (for Vertex AI)

### Required IAM Setup (Project Owner Only)

```bash
# Grant Vertex AI permissions
gcloud projects add-iam-policy-binding vibe-match-463114 \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/aiplatform.user"

# Verify permissions
gcloud projects get-iam-policy vibe-match-463114 \
    --flatten="bindings[].members" \
    --filter="bindings.members:<EMAIL>" \
    --format="table(bindings.role)"
```

### Testing AI Access

```bash
# Test Gemini 2.5 Flash
curl -X POST \
  -H "Authorization: Bearer $(gcloud auth print-access-token)" \
  -H "Content-Type: application/json" \
  -d '{"contents":[{"role":"user","parts":[{"text":"Test"}]}]}' \
  "https://us-central1-aiplatform.googleapis.com/v1/projects/vibe-match-463114/locations/us-central1/publishers/google/models/gemini-2.5-flash:generateContent"
```

---

## 🔍 Troubleshooting

### Common Issues

#### Service Won't Start
1. Check environment variables
2. Verify GCP credentials
3. Ensure Spanner connectivity
4. Check port availability

#### AI Features Not Working
1. Verify Vertex AI permissions
2. Check API enablement
3. Validate service account
4. Review quota limits

#### Performance Issues
1. Check concurrent analysis limit
2. Monitor memory usage
3. Review cache hit rates
4. Analyze slow queries

### Debug Commands

```bash
# Check service logs
kubectl logs -f deployment/analysis-engine

# Test database connectivity
./scripts/testing/test_database_operations.sh

# Validate AI integration
./scripts/testing/test_ai_integration.sh

# Run all tests
./scripts/testing/run_all_tests.sh

# Validate deployment
./scripts/validation/validate_all.sh

# Performance profiling
RUST_LOG=debug cargo run --release
```

### Support Contacts

- **Platform Team**: <EMAIL>
- **On-Call**: Use PagerDuty
- **Slack**: #analysis-engine-support

---

## 📚 Additional Resources

- [API Documentation](/docs/api/)
- [Architecture Overview](/docs/architecture/)
- [Security Configuration](/docs/security/)
- [Development Guide](/docs/development/)

---

*Last Updated: July 10, 2025*