// services/analysis-engine/build.rs

use std::collections::HashSet;
use std::env;
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;
use walkdir::WalkDir;

/// Main entry point for the build script.
///
/// This script is responsible for the following:
/// 1.  **Finding `tree-sitter-*` grammar crates**: It parses `Cargo.toml` metadata
///     to identify all direct and transitive dependencies that follow the `tree-sitter-*` naming convention.
/// 2.  **Locating Grammar Source Files**: For each grammar crate, it finds the `src` directory
///     containing the C/C++ source files (`parser.c`, `scanner.c`, `scanner.cc`, etc.).
/// 3.  **Compiling Grammars**: It uses the `cc` crate to compile the C/C++ source files
///     into a single static library (`tree-sitter-grammars.a`).
/// 4.  **Handling C++ Scanners**: It correctly detects and compiles C++ scanners (`scanner.cc` or `scanner.cpp`)
///     by enabling the C++ compiler in the `cc` crate.
/// 5.  **Linking the Static Library**: It instructs `rustc` to link the compiled static library
///     into the final `analysis-engine` binary.
/// 6.  **Generating Language Bindings**: It generates a Rust source file (`language_bindings.rs`)
///     that contains the necessary `extern "C"` blocks to link the compiled grammar functions.
fn main() {
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());
    let grammar_paths = find_tree_sitter_grammars();

    let mut config = cc::Build::new();
    config.opt_level(3);
    config.warnings(false);

    let mut cpp = false;
    let mut files = Vec::new();

    for (_, grammar_path) in &grammar_paths {
        for entry in WalkDir::new(grammar_path)
            .into_iter()
            .filter_map(|e| e.ok())
        {
            let path = entry.path();
            if let Some(extension) = path.extension() {
                if extension == "c" {
                    files.push(path.to_path_buf());
                    if let Some(parent) = path.parent() {
                        config.include(parent);
                    }
                } else if extension == "cc" || extension == "cpp" {
                    files.push(path.to_path_buf());
                    cpp = true;
                    if let Some(parent) = path.parent() {
                        config.include(parent);
                    }
                }
            }
        }
    }

    config.files(&files);

    if cpp {
        config.cpp(true);
    }

    config.compile("tree-sitter-grammars");

    generate_language_bindings(&out_dir, &grammar_paths);
}

/// Finds all `tree-sitter-*` grammar crates in the dependency graph.
///
/// It uses the `cargo metadata` command to get a list of all dependencies
/// and filters them to find the ones that are tree-sitter grammars.
///
/// # Returns
///
/// A vector of `PathBuf`s, where each path points to the root directory of a grammar crate.
fn find_tree_sitter_grammars() -> Vec<(String, PathBuf)> {
    let metadata = Command::new("cargo")
        .arg("metadata")
        .arg("--format-version=1")
        .output()
        .expect("Failed to run cargo metadata");

    let metadata: serde_json::Value =
        serde_json::from_slice(&metadata.stdout).expect("Failed to parse cargo metadata");

    let packages = metadata["packages"].as_array().unwrap();
    let mut grammar_packages = HashSet::new();

    for package in packages {
        let name = package["name"].as_str().unwrap();
        if name.starts_with("tree-sitter-") && name != "tree-sitter-language" {
            let manifest_path = PathBuf::from(package["manifest_path"].as_str().unwrap());
            grammar_packages.insert((name.to_string(), manifest_path.parent().unwrap().to_path_buf()));
        }
    }

    grammar_packages.into_iter().collect()
}

/// Generates a Rust source file with `extern "C"` blocks for each language.
///
/// This allows the Rust code to call the C functions from the compiled static library.
/// The generated file is placed in the `OUT_DIR` and included in the main crate.
///
/// # Arguments
///
/// * `out_dir` - The directory where the generated file should be saved.
/// * `grammar_paths` - A slice of paths to the grammar crates.
fn generate_language_bindings(out_dir: &Path, grammars: &[(String, PathBuf)]) {
    let mut bindings = String::new();
    bindings.push_str("use tree_sitter::Language;\n\n");
    bindings.push_str("extern \"C\" {\n");

    for (name, _) in grammars {
        let function_name = if name == "tree-sitter-md" {
            "tree_sitter_markdown".to_string()
        } else {
            format!("tree_sitter_{}", name.replace("tree-sitter-", "").replace("-", "_"))
        };
        bindings.push_str(&format!("    fn {}() -> Language;\n", function_name));
    }
    bindings.push_str("}\n\n");

    bindings.push_str("pub fn get_language(name: &str) -> Option<Language> {\n");
    bindings.push_str("    match name {\n");
    for (name, _) in grammars {
        let grammar_name = name.replace("tree-sitter-", "");
        let function_name = if name == "tree-sitter-md" {
            "tree_sitter_markdown".to_string()
        } else {
            format!("tree_sitter_{}", grammar_name.replace("-", "_"))
        };
        bindings.push_str(&format!(
            "        \"{}\" => Some(unsafe {{ {}() }}),\n",
            grammar_name, function_name
        ));
    }
    bindings.push_str("        _ => None,\n");
    bindings.push_str("    }\n");
    bindings.push_str("}\n\n");

    bindings.push_str("pub const SUPPORTED_LANGUAGES: &[&str] = &[\n");
    for (name, _) in grammars {
        let grammar_name = name.replace("tree-sitter-", "");
        bindings.push_str(&format!("    \"{}\",\n", grammar_name));
    }
    bindings.push_str("];\n");

    let dest_path = out_dir.join("language_bindings.rs");
    fs::write(&dest_path, bindings).unwrap();
}