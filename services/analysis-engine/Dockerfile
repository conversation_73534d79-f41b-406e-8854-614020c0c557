# ============================================
# Multi-stage Dockerfile for Analysis Engine
# Supports both standard and Cloud Run deployment
# ============================================

# Stage 1: Dependencies caching
FROM rust:latest AS dependencies

WORKDIR /usr/src/app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy only Cargo files for dependency caching
COPY Cargo.toml Cargo.lock ./

# Create dummy main.rs to build dependencies
RUN mkdir -p src/bin && \
    echo "fn main() {}" > src/main.rs && \
    echo "fn main() {}" > src/bin/test_ai_services.rs && \
    echo "fn main() {}" > src/bin/analysis_bench.rs && \
    cargo build --release && \
    rm -rf src

# Stage 2: Builder
FROM rust:latest AS builder

WORKDIR /usr/src/app

# Install build dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy cached dependencies from previous stage
COPY --from=dependencies /usr/src/app/target target
COPY --from=dependencies /usr/local/cargo /usr/local/cargo

# Copy source code
COPY Cargo.toml Cargo.lock ./
COPY src ./src
COPY benches ./benches

# Build with aggressive optimizations
ENV CARGO_BUILD_JOBS=8
RUN cargo build --release \
    && strip /usr/src/app/target/release/analysis-engine

# Stage 3: Runtime (choose based on deployment target)
# For standard deployment with shell access
FROM debian:bookworm-slim AS runtime-standard

RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    curl \
    && rm -rf /var/lib/apt/lists/*

RUN useradd -m -u 1001 appuser

# Stage 4: Distroless runtime for Cloud Run
FROM gcr.io/distroless/cc-debian12:nonroot AS runtime-distroless

# Copy SSL certificates
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Final stage: Choose runtime based on build arg
ARG RUNTIME_TARGET=standard
FROM runtime-${RUNTIME_TARGET} AS final

# Copy the binary
COPY --from=builder /usr/src/app/target/release/analysis-engine /app/analysis-engine

# For standard runtime, set ownership and user
RUN if [ "$RUNTIME_TARGET" = "standard" ]; then \
        chown appuser:appuser /app/analysis-engine && \
        chmod +x /app/analysis-engine; \
    fi

# Set user (distroless already runs as nonroot)
USER ${RUNTIME_TARGET:+appuser}

# Environment variables
ENV RUST_LOG=info \
    RUST_BACKTRACE=1

# Default to port 8001, but allow override via PORT env var for Cloud Run
ENV PORT=8001
ENV ANALYSIS_ENGINE_ADDR=0.0.0.0:${PORT}

# Health check for standard runtime only
RUN if [ "$RUNTIME_TARGET" = "standard" ]; then \
        echo 'HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
            CMD curl -f http://localhost:${PORT}/health || exit 1' >> /tmp/healthcheck; \
    fi

# Expose port
EXPOSE ${PORT}

# Run the binary
ENTRYPOINT ["/app/analysis-engine"]

# Build examples:
# Standard deployment: docker build -t analysis-engine:latest .
# Cloud Run deployment: docker build -t analysis-engine:cloudrun --build-arg RUNTIME_TARGET=distroless .