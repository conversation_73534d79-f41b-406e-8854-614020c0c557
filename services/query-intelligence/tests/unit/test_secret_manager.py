"""
Unit tests for SecretManagerService
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import os

from query_intelligence.services.secret_manager import (
    SecretManagerService,
    get_secret_manager_service,
    get_jwt_secret,
    get_pinecone_api_key,
    get_google_api_key,
)


class TestSecretManagerService:
    """Test cases for SecretManagerService"""

    def setup_method(self):
        """Set up test fixtures"""
        # Clear any cached instances
        import query_intelligence.services.secret_manager
        query_intelligence.services.secret_manager._secret_manager_service = None

    def test_init_with_default_settings(self):
        """Test initialization with default test settings"""
        service = SecretManagerService()
        
        # In test environment, USE_SECRET_MANAGER should be False
        assert service.use_secret_manager is False
        assert service._client is None
        # project_id might be None in test environment
        assert hasattr(service, 'project_id')

    def test_get_secret_with_secret_manager_disabled(self):
        """Test get_secret when SECRET_MANAGER is disabled"""
        service = SecretManagerService()
        
        result = service.get_secret("test-secret")
        
        assert result is None

    def test_get_secret_or_env_with_env_var(self):
        """Test get_secret_or_env with environment variable"""
        service = SecretManagerService()
        
        with patch.dict(os.environ, {'TEST_ENV_VAR': 'env-value'}):
            result = service.get_secret_or_env("nonexistent-secret", "TEST_ENV_VAR", "default-value")
            assert result == "env-value"

    def test_get_secret_or_env_use_default(self):
        """Test get_secret_or_env using default value"""
        service = SecretManagerService()
        
        with patch.dict(os.environ, {}, clear=True):
            result = service.get_secret_or_env("nonexistent-secret", "NONEXISTENT_ENV", "default-value")
            assert result == "default-value"

    def test_get_secret_or_env_no_default(self):
        """Test get_secret_or_env with no default value"""
        service = SecretManagerService()
        
        with patch.dict(os.environ, {}, clear=True):
            result = service.get_secret_or_env("nonexistent-secret", "NONEXISTENT_ENV")
            assert result is None

    def test_validate_production_secrets_non_production(self):
        """Test validate_production_secrets in non-production environment"""
        service = SecretManagerService()
        
        # In test environment, this should indicate non-production
        result = service.validate_production_secrets()
        
        # Should be valid in non-production
        assert result["valid"] is True
        assert "Not in production environment" in result["warnings"]

    def test_refresh_cache(self):
        """Test refresh_cache method exists and can be called"""
        service = SecretManagerService()
        
        # Should not raise any exceptions
        service.refresh_cache()
        
        # Method should exist
        assert hasattr(service, 'refresh_cache')

    def test_get_secret_manager_service_singleton(self):
        """Test that get_secret_manager_service returns singleton instance"""
        service1 = get_secret_manager_service()
        service2 = get_secret_manager_service()
        
        assert service1 is service2

    def test_get_jwt_secret_from_env(self):
        """Test get_jwt_secret gets value from environment (test setup)"""
        result = get_jwt_secret()
        # Should get the test value from conftest.py
        assert result == "test-secret-key"

    def test_get_pinecone_api_key_from_env(self):
        """Test get_pinecone_api_key gets value from environment (test setup)"""
        result = get_pinecone_api_key()
        # Should get the test value from conftest.py
        assert result == "test-pinecone-key"

    def test_get_google_api_key_from_env(self):
        """Test get_google_api_key gets value from environment (test setup)"""
        result = get_google_api_key()
        # Should get the test value from conftest.py
        assert result == "test-api-key"

    def test_get_pinecone_api_key_not_found(self):
        """Test get_pinecone_api_key when key is not found"""
        # Clear the cached instance to avoid conflicts
        import query_intelligence.services.secret_manager
        query_intelligence.services.secret_manager._secret_manager_service = None
        
        with patch.dict(os.environ, {}, clear=True):
            result = get_pinecone_api_key()
            assert result is None

    def test_get_google_api_key_not_found(self):
        """Test get_google_api_key when key is not found"""
        # Clear the cached instance to avoid conflicts
        import query_intelligence.services.secret_manager
        query_intelligence.services.secret_manager._secret_manager_service = None
        
        with patch.dict(os.environ, {}, clear=True):
            result = get_google_api_key()
            assert result is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_enabled_with_successful_client(self, mock_client):
        """Test secret manager when enabled with successful client creation"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock the response
        mock_response = Mock()
        mock_response.payload.data.decode.return_value = "secret-value"
        mock_client_instance.access_secret_version.return_value = mock_response
        
        # Create service with mocked settings
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret")
                assert result == "secret-value"
                mock_client_instance.access_secret_version.assert_called_once()

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_exception_handling(self, mock_client):
        """Test secret manager exception handling"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock exception
        mock_client_instance.access_secret_version.side_effect = Exception("Secret not found")
        
        # Create service with mocked settings
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("nonexistent-secret")
                assert result is None

    def test_secret_manager_client_not_initialized(self):
        """Test behavior when secret manager client is not initialized"""
        service = SecretManagerService()
        
        # Simulate client not being initialized
        original_client = service._client
        service._client = None
        
        result = service.get_secret("test-secret")
        assert result is None
        
        # Restore original client
        service._client = original_client

    def test_validate_production_secrets_structure(self):
        """Test that validate_production_secrets returns proper structure"""
        service = SecretManagerService()
        
        result = service.validate_production_secrets()
        
        # Should have proper structure
        assert "valid" in result
        assert "errors" in result
        assert "warnings" in result
        assert isinstance(result["valid"], bool)
        assert isinstance(result["errors"], list)
        assert isinstance(result["warnings"], list)

    def test_secret_manager_production_validation_with_env_vars(self):
        """Test production validation with environment variables"""
        service = SecretManagerService()
        
        # Test with production environment
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'proper-secret-value',
            'PINECONE_API_KEY': 'proper-pinecone-value'
        }):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            # Should validate based on environment setup
            assert isinstance(result["valid"], bool)

    def test_secret_manager_production_validation_missing_secrets(self):
        """Test production validation with missing secrets"""
        service = SecretManagerService()
        
        # Test with production environment and no secrets
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            # Should have errors for missing secrets
            assert isinstance(result["errors"], list)

    def test_secret_manager_production_validation_default_values(self):
        """Test production validation with default values"""
        service = SecretManagerService()
        
        # Test with production environment and default values
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'CHANGE-THIS-USE-SECRET-MANAGER',
            'PINECONE_API_KEY': 'CHANGE-THIS-USE-SECRET-MANAGER'
        }):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            # Should have errors for default values
            assert isinstance(result["errors"], list)

    def test_get_jwt_secret_production_validation(self):
        """Test JWT secret production validation"""
        # Test with production environment and default value
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            # Need to reload settings to pick up the environment change
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                # Should raise ValueError in production with default value
                with pytest.raises(ValueError, match="JWT secret key must be set in production"):
                    get_jwt_secret()

    def test_get_jwt_secret_non_production_default(self):
        """Test JWT secret in non-production with default"""
        # Test with development environment and no JWT secret
        with patch.dict(os.environ, {'ENVIRONMENT': 'development'}, clear=True):
            # Clear cached instance
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            result = get_jwt_secret()
            
            # Should return default value
            assert result == "CHANGE-THIS-USE-SECRET-MANAGER"

    def test_secret_manager_service_attributes(self):
        """Test that SecretManagerService has expected attributes"""
        service = SecretManagerService()
        
        assert hasattr(service, 'use_secret_manager')
        assert hasattr(service, 'project_id')
        assert hasattr(service, '_client')
        assert hasattr(service, 'get_secret')
        assert hasattr(service, 'get_secret_or_env')
        assert hasattr(service, 'validate_production_secrets')
        assert hasattr(service, 'refresh_cache')

    def test_secret_manager_service_methods_callable(self):
        """Test that SecretManagerService methods are callable"""
        service = SecretManagerService()
        
        # These should not raise exceptions
        service.get_secret("test")
        service.get_secret_or_env("test", "TEST_ENV", "default")
        service.validate_production_secrets()
        service.refresh_cache()

    def test_global_helper_functions_callable(self):
        """Test that global helper functions are callable"""
        # These should not raise exceptions
        get_secret_manager_service()
        get_jwt_secret()
        get_pinecone_api_key()
        get_google_api_key()

    def test_secret_manager_with_version_parameter(self):
        """Test secret retrieval with version parameter"""
        service = SecretManagerService()
        
        # Should handle version parameter gracefully
        result = service.get_secret("test-secret", version="2")
        
        # With SECRET_MANAGER disabled, should return None
        assert result is None

    def test_secret_caching_behavior(self):
        """Test that secret caching works properly"""
        service = SecretManagerService()
        
        # Test that get_secret has cache_clear method (from lru_cache)
        assert hasattr(service.get_secret, 'cache_clear')
        
        # Test that refresh_cache calls cache_clear
        service.refresh_cache()  # Should not raise exceptions

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_with_version_specific_retrieval(self, mock_client):
        """Test secret retrieval with specific version"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock the response
        mock_response = Mock()
        mock_response.payload.data.decode.return_value = "versioned-secret-value"
        mock_client_instance.access_secret_version.return_value = mock_response
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret", version="3")
                assert result == "versioned-secret-value"
                
                # Verify correct version path was used
                call_args = mock_client_instance.access_secret_version.call_args
                assert "versions/3" in str(call_args)

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_network_timeout(self, mock_client):
        """Test secret manager handling network timeouts"""
        from google.api_core.exceptions import DeadlineExceeded
        
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.access_secret_version.side_effect = DeadlineExceeded("Timeout")
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret")
                assert result is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_permission_denied(self, mock_client):
        """Test secret manager handling permission denied"""
        from google.api_core.exceptions import PermissionDenied
        
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.access_secret_version.side_effect = PermissionDenied("Access denied")
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret")
                assert result is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_not_found(self, mock_client):
        """Test secret manager handling secret not found"""
        from google.api_core.exceptions import NotFound
        
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        mock_client_instance.access_secret_version.side_effect = NotFound("Secret not found")
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("nonexistent-secret")
                assert result is None

    def test_secret_manager_initialization_no_project_id(self):
        """Test secret manager initialization without project ID"""
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true'}, clear=True):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            # Should fall back to disabled mode
            assert service.use_secret_manager is False or service._client is None

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_client_creation_failure(self, mock_client):
        """Test secret manager when client creation fails"""
        mock_client.side_effect = Exception("Client creation failed")
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            # Should handle client creation failure gracefully
            assert service._client is None

    def test_get_secret_or_env_priority_order(self):
        """Test get_secret_or_env priority: secret manager > env var > default"""
        service = SecretManagerService()
        
        # Test env var takes precedence over default when secret manager disabled
        with patch.dict(os.environ, {'TEST_VAR': 'env-value'}):
            result = service.get_secret_or_env("test-secret", "TEST_VAR", "default-value")
            assert result == "env-value"
        
        # Test default is used when neither secret manager nor env var available
        with patch.dict(os.environ, {}, clear=True):
            result = service.get_secret_or_env("test-secret", "MISSING_VAR", "default-value")
            assert result == "default-value"

    def test_production_validation_comprehensive_errors(self):
        """Test production validation identifies all types of errors"""
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'CHANGE-THIS-USE-SECRET-MANAGER',  # Default value
            'PINECONE_API_KEY': '',  # Empty value
            # GOOGLE_API_KEY missing entirely
        }):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            assert result["valid"] is False
            assert len(result["errors"]) >= 2  # At least JWT and Pinecone errors

    def test_production_validation_with_proper_secrets(self):
        """Test production validation passes with proper secrets"""
        with patch.dict(os.environ, {
            'ENVIRONMENT': 'production',
            'JWT_SECRET_KEY': 'proper-production-jwt-secret-32-chars-long',
            'PINECONE_API_KEY': 'proper-pinecone-api-key',
            'GOOGLE_API_KEY': 'proper-google-api-key'
        }):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            result = service.validate_production_secrets()
            
            assert result["valid"] is True
            assert len(result["errors"]) == 0

    def test_get_google_api_key_production_validation(self):
        """Test Google API key production validation"""
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                with pytest.raises(ValueError, match="Google API key must be set in production"):
                    get_google_api_key()

    def test_get_pinecone_api_key_production_validation(self):
        """Test Pinecone API key production validation"""
        with patch.dict(os.environ, {'ENVIRONMENT': 'production'}, clear=True):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            with patch('query_intelligence.services.secret_manager.settings') as mock_settings:
                mock_settings.is_production.return_value = True
                
                with pytest.raises(ValueError, match="Pinecone API key must be set in production"):
                    get_pinecone_api_key()

    def test_secret_manager_cache_behavior(self):
        """Test secret manager caching behavior"""
        service = SecretManagerService()
        
        # Test cache info exists
        assert hasattr(service.get_secret, 'cache_info')
        
        # Test cache clear functionality
        initial_info = service.get_secret.cache_info()
        service.refresh_cache()
        post_clear_info = service.get_secret.cache_info()
        
        # Cache should be cleared
        assert post_clear_info.currsize == 0

    @patch('google.cloud.secretmanager.SecretManagerServiceClient')
    def test_secret_manager_response_decoding_error(self, mock_client):
        """Test secret manager handling response decoding errors"""
        mock_client_instance = Mock()
        mock_client.return_value = mock_client_instance
        
        # Mock response with decode error
        mock_response = Mock()
        mock_response.payload.data.decode.side_effect = UnicodeDecodeError(
            'utf-8', b'invalid', 0, 1, 'invalid utf-8'
        )
        mock_client_instance.access_secret_version.return_value = mock_response
        
        with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
            import query_intelligence.services.secret_manager
            query_intelligence.services.secret_manager._secret_manager_service = None
            
            service = SecretManagerService()
            
            if service.use_secret_manager and service._client:
                result = service.get_secret("test-secret")
                assert result is None

    def test_secret_manager_empty_response(self):
        """Test secret manager handling empty response"""
        with patch('google.cloud.secretmanager.SecretManagerServiceClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            # Mock empty response
            mock_response = Mock()
            mock_response.payload.data.decode.return_value = ""
            mock_client_instance.access_secret_version.return_value = mock_response
            
            with patch.dict(os.environ, {'USE_SECRET_MANAGER': 'true', 'GCP_PROJECT_ID': 'test-project'}):
                import query_intelligence.services.secret_manager
                query_intelligence.services.secret_manager._secret_manager_service = None
                
                service = SecretManagerService()
                
                if service.use_secret_manager and service._client:
                    result = service.get_secret("empty-secret")
                    assert result == ""

    def test_environment_variable_edge_cases(self):
        """Test environment variable handling edge cases"""
        service = SecretManagerService()
        
        # Test with empty string environment variable
        with patch.dict(os.environ, {'EMPTY_VAR': ''}):
            result = service.get_secret_or_env("test", "EMPTY_VAR", "default")
            assert result == ""  # Empty string should be returned, not default
        
        # Test with whitespace-only environment variable
        with patch.dict(os.environ, {'WHITESPACE_VAR': '   '}):
            result = service.get_secret_or_env("test", "WHITESPACE_VAR", "default")
            assert result == "   "  # Whitespace should be preserved

    def test_secret_manager_service_thread_safety(self):
        """Test that secret manager service is thread-safe"""
        import threading
        import time
        
        results = []
        errors = []
        
        def get_service():
            try:
                service = get_secret_manager_service()
                results.append(id(service))
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads
        threads = [threading.Thread(target=get_service) for _ in range(10)]
        
        # Start all threads
        for t in threads:
            t.start()
        
        # Wait for completion
        for t in threads:
            t.join()
        
        # Should have no errors and all services should be the same instance
        assert len(errors) == 0
        assert len(set(results)) == 1  # All should be same instance ID