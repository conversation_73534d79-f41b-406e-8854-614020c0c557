import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch
from query_intelligence.main import app  # Assuming the main app is in main.py
from query_intelligence.config.settings import get_settings

# Override settings for testing
settings = get_settings()
settings.JWT_SECRET_KEY = "test_secret"


@pytest.fixture
def client():
    """Test client for the FastAPI app"""
    return TestClient(app)


@pytest.fixture
def mock_redis_client():
    """Mock Redis client"""
    mock = AsyncMock()
    mock.get.return_value = "0"
    mock.scard.return_value = 0
    mock.zrevrange.return_value = []
    mock.hgetall.return_value = {}
    mock.info.return_value = {}
    return mock


@pytest.fixture(autouse=True)
def override_dependencies(mock_redis_client):
    """Override dependencies for all tests"""
    with patch(
        "query_intelligence.api.admin.get_redis_client",
        return_value=mock_redis_client,
    ), patch(
        "query_intelligence.api.admin.get_cache_manager",
        return_value=AsyncMock(),
    ), patch(
        "query_intelligence.middleware.auth.jwt.decode",
        return_value={"sub": "admin", "exp": **********},
    ):
        yield


def get_admin_headers():
    """Helper to get admin headers"""
    # This is a simplified way to get admin headers.
    # In a real application, you would generate a valid JWT token.
    return {"Authorization": "Bearer admin_token"}


class TestAdminAPI:
    """Test suite for the Admin API"""

    def test_get_metrics_unauthorized(self, client):
        """Test that metrics endpoint requires authentication"""
        response = client.get("/admin/metrics")
        assert response.status_code == 401

    def test_get_metrics_success(self, client, mock_redis_client):
        """Test successful retrieval of system metrics"""
        mock_redis_client.get.side_effect = [
            "100",  # total_queries
            "80",  # cache_hits
            "20",  # cache_misses
            "12345.0",  # total_response_time
            "5",  # error_count
            "10",  # qpm_count
        ]
        mock_redis_client.scard.return_value = 10
        response = client.get("/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert data["total_queries"] == 100
        assert data["cache_hit_rate"] == 0.8

    def test_get_health_success(self, client, mock_redis_client):
        """Test successful retrieval of service health"""
        mock_redis_client.ping.return_value = True
        mock_redis_client.get.side_effect = ["healthy", "healthy", "healthy"]
        with patch(
            "query_intelligence.api.admin.get_circuit_breaker_status",
            return_value={"test_breaker": {"state": "closed"}},
        ):
            response = client.get("/admin/health", headers=get_admin_headers())
            assert response.status_code == 200
            data = response.json()
            assert data["redis"] == "healthy"
            assert data["circuit_breakers"]["test_breaker"] == "closed"

    def test_get_query_stats_success(self, client, mock_redis_client):
        """Test successful retrieval of query statistics"""
        mock_redis_client.zrevrange.return_value = [("test query", 10)]
        mock_redis_client.hgetall.return_value = {"en": "5", "es": "5"}
        response = client.get("/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        assert len(data["top_queries"]) == 1
        assert data["language_distribution"]["en"] == 5

    def test_clear_cache_success(self, client):
        """Test successful cache clearing"""
        response = client.post(
            "/admin/cache/clear?cache_type=all", headers=get_admin_headers()
        )
        assert response.status_code == 200
        assert response.json() == {"status": "success", "cleared": "all"}

    def test_clear_cache_memory_only(self, client):
        """Test clearing memory cache only"""
        response = client.post(
            "/admin/cache/clear?cache_type=memory", headers=get_admin_headers()
        )
        assert response.status_code == 200
        assert response.json() == {"status": "success", "cleared": "memory"}

    def test_clear_cache_redis_only(self, client):
        """Test clearing Redis cache only"""
        response = client.post(
            "/admin/cache/clear?cache_type=redis", headers=get_admin_headers()
        )
        assert response.status_code == 200
        assert response.json() == {"status": "success", "cleared": "redis"}

    def test_clear_cache_unauthorized(self, client):
        """Test cache clear requires admin authorization"""
        response = client.post("/admin/cache/clear")
        assert response.status_code == 401

    def test_get_cache_stats_success(self, client, mock_redis_client):
        """Test successful retrieval of cache statistics"""
        mock_redis_client.get.side_effect = ["100", "150"]  # memory hits, total
        mock_redis_client.info.return_value = {
            "db0": {"keys": 50},
            "used_memory": 1048576  # 1MB in bytes
        }
        
        with patch(
            "query_intelligence.api.admin.get_cache_manager"
        ) as mock_cache_manager:
            mock_cache = mock_cache_manager.return_value
            mock_cache._query_memory_cache = {i: f"query_{i}" for i in range(10)}
            mock_cache._embedding_memory_cache = {i: f"embed_{i}" for i in range(5)}
            mock_cache._access_counts = {f"query_{i}": i for i in range(20)}
            
            response = client.get("/admin/cache/stats", headers=get_admin_headers())
            assert response.status_code == 200
            data = response.json()
            assert data["memory_cache_size"] == 15
            assert data["redis_keys"] == 50
            assert data["redis_memory_mb"] == 1.0
            assert len(data["hot_queries"]) <= 10

    def test_get_cache_stats_unauthorized(self, client):
        """Test cache stats requires admin authorization"""
        response = client.get("/admin/cache/stats")
        assert response.status_code == 401

    def test_reset_circuit_breaker_specific(self, client):
        """Test resetting a specific circuit breaker"""
        with patch(
            "query_intelligence.api.admin.reset_circuit_breaker"
        ) as mock_reset:
            response = client.post(
                "/admin/circuit-breakers/reset?breaker_name=test_breaker",
                headers=get_admin_headers()
            )
            assert response.status_code == 200
            assert response.json() == {"status": "success", "reset": "test_breaker"}
            mock_reset.assert_called_once_with("test_breaker")

    def test_reset_circuit_breaker_all(self, client):
        """Test resetting all circuit breakers"""
        with patch(
            "query_intelligence.api.admin.get_circuit_breaker_status",
            return_value={"breaker1": {}, "breaker2": {}}
        ), patch(
            "query_intelligence.api.admin.reset_circuit_breaker"
        ) as mock_reset:
            response = client.post(
                "/admin/circuit-breakers/reset",
                headers=get_admin_headers()
            )
            assert response.status_code == 200
            assert response.json() == {"status": "success", "reset": "all"}
            assert mock_reset.call_count == 2

    def test_reset_circuit_breaker_unauthorized(self, client):
        """Test circuit breaker reset requires admin authorization"""
        response = client.post("/admin/circuit-breakers/reset")
        assert response.status_code == 401

    def test_get_configuration_success(self, client):
        """Test successful retrieval of sanitized configuration"""
        response = client.get("/admin/config", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        # Check for expected configuration keys
        expected_keys = [
            "environment", "service_name", "port", "cache_ttl_hours",
            "rate_limit_requests", "max_query_length", "llm_model",
            "enable_metrics", "circuit_breaker_config"
        ]
        for key in expected_keys:
            assert key in data
        # Ensure no sensitive data is exposed
        assert "jwt_secret" not in str(data).lower()
        assert "api_key" not in str(data).lower()

    def test_get_configuration_unauthorized(self, client):
        """Test configuration endpoint requires admin authorization"""
        response = client.get("/admin/config")
        assert response.status_code == 401

    def test_metrics_error_handling(self, client, mock_redis_client):
        """Test metrics endpoint handles Redis errors gracefully"""
        mock_redis_client.get.side_effect = Exception("Redis connection failed")
        
        response = client.get("/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 500
        assert "Failed to retrieve metrics" in response.json()["detail"]

    def test_health_redis_down(self, client, mock_redis_client):
        """Test health check when Redis is down"""
        mock_redis_client.ping.side_effect = Exception("Connection failed")
        mock_redis_client.get.side_effect = ["healthy", "healthy", "healthy"]
        
        with patch(
            "query_intelligence.api.admin.get_circuit_breaker_status",
            return_value={}
        ):
            response = client.get("/admin/health", headers=get_admin_headers())
            assert response.status_code == 200
            data = response.json()
            assert data["redis"] == "unhealthy"

    def test_query_stats_error_handling(self, client, mock_redis_client):
        """Test query stats handles errors gracefully"""
        mock_redis_client.zrevrange.side_effect = Exception("Redis error")
        
        response = client.get("/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 500
        assert "Failed to retrieve query statistics" in response.json()["detail"]

    def test_cache_stats_error_handling(self, client, mock_redis_client):
        """Test cache stats handles errors gracefully"""
        mock_redis_client.info.side_effect = Exception("Redis info failed")
        
        response = client.get("/admin/cache/stats", headers=get_admin_headers())
        assert response.status_code == 500
        assert "Failed to retrieve cache statistics" in response.json()["detail"]

    def test_clear_cache_error_handling(self, client):
        """Test cache clear handles errors gracefully"""
        with patch(
            "query_intelligence.api.admin.get_cache_manager"
        ) as mock_cache_manager:
            mock_cache_manager.return_value._init_memory_cache.side_effect = Exception("Clear failed")
            
            response = client.post(
                "/admin/cache/clear?cache_type=memory", headers=get_admin_headers()
            )
            assert response.status_code == 500
            assert "Failed to clear cache" in response.json()["detail"]

    def test_circuit_breaker_reset_error_handling(self, client):
        """Test circuit breaker reset handles errors gracefully"""
        with patch(
            "query_intelligence.api.admin.reset_circuit_breaker",
            side_effect=Exception("Reset failed")
        ):
            response = client.post(
                "/admin/circuit-breakers/reset?breaker_name=test",
                headers=get_admin_headers()
            )
            assert response.status_code == 500
            assert "Failed to reset circuit breaker" in response.json()["detail"]

    def test_admin_endpoints_non_admin_user(self, client):
        """Test that non-admin users cannot access admin endpoints"""
        # Mock a non-admin user token
        with patch(
            "query_intelligence.middleware.auth.jwt.decode",
            return_value={"sub": "regular_user", "roles": ["user"], "exp": **********}
        ):
            non_admin_headers = {"Authorization": "Bearer user_token"}
            
            # Test all admin endpoints
            endpoints = [
                ("/admin/metrics", "GET"),
                ("/admin/health", "GET"),
                ("/admin/queries/stats", "GET"),
                ("/admin/cache/stats", "GET"),
                ("/admin/config", "GET"),
                ("/admin/cache/clear", "POST"),
                ("/admin/circuit-breakers/reset", "POST")
            ]
            
            for endpoint, method in endpoints:
                if method == "GET":
                    response = client.get(endpoint, headers=non_admin_headers)
                else:
                    response = client.post(endpoint, headers=non_admin_headers)
                assert response.status_code == 403

    def test_query_stats_comprehensive_data(self, client, mock_redis_client):
        """Test query stats with comprehensive data"""
        # Mock all the data retrieval calls
        mock_redis_client.zrevrange.return_value = [
            ("how to authenticate", 25),
            ("find user model", 20),
            ("debug login issue", 15)
        ]
        mock_redis_client.get.side_effect = [
            "50",   # EXPLAIN intent
            "30",   # FIND intent  
            "15",   # DEBUG intent
            "5",    # ANALYZE intent
            "10",   # COMPARE intent
            "5",    # UNKNOWN intent
            "425.0", # total_confidence
            "100"   # total_queries
        ]
        mock_redis_client.hgetall.return_value = {
            "python": "60",
            "javascript": "25", 
            "typescript": "15"
        }
        
        response = client.get("/admin/queries/stats", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        # Verify top queries
        assert len(data["top_queries"]) == 3
        assert data["top_queries"][0]["query"] == "how to authenticate"
        assert data["top_queries"][0]["count"] == 25
        
        # Verify intent distribution
        assert data["query_intents"]["EXPLAIN"] == 50
        assert data["query_intents"]["FIND"] == 30
        
        # Verify language distribution
        assert data["language_distribution"]["python"] == 60
        assert data["language_distribution"]["javascript"] == 25
        
        # Verify average confidence
        assert data["average_confidence"] == 4.25

    def test_metrics_calculation_edge_cases(self, client, mock_redis_client):
        """Test metrics calculations with edge cases"""
        # Test division by zero cases
        mock_redis_client.get.side_effect = [
            "0",  # total_queries
            "0",  # cache_hits
            "0",  # cache_misses
            "0",  # total_response_time
            "0",  # error_count
            "0",  # qpm_count for minute 0
            "0",  # qpm_count for minute 1
            "0",  # qpm_count for minute 2
            "0",  # qpm_count for minute 3
            "0",  # qpm_count for minute 4
        ]
        mock_redis_client.scard.return_value = 0
        
        response = client.get("/admin/metrics", headers=get_admin_headers())
        assert response.status_code == 200
        data = response.json()
        
        # All rates should be 0.0 when no data
        assert data["cache_hit_rate"] == 0.0
        assert data["average_response_time_ms"] == 0.0
        assert data["error_rate"] == 0.0
        assert data["queries_per_minute"] == 0.0
