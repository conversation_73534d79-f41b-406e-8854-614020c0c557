import pytest
import json
from unittest.mock import patch, As<PERSON><PERSON><PERSON>, <PERSON><PERSON>
from fastapi.testclient import Test<PERSON><PERSON>

from query_intelligence.main import app
from query_intelligence.models import (
    QueryIntent,
    IntentAnalysis,
    CodeChunk,
    SearchResult,
    CodeReference,
)
from query_intelligence.middleware.auth import jwt_auth


@pytest.fixture
def test_client():
    return TestClient(app)


@pytest.fixture
def mock_query_processor():
    mock = Mock()
    mock._create_context = Mock()
    mock._analyze_intent = AsyncMock()
    mock.semantic_search = Mock()
    mock.semantic_search.generate_embedding = AsyncMock()
    mock.semantic_search.search = AsyncMock()
    mock._build_search_filters = Mock()
    mock._rerank_chunks = AsyncMock()
    mock._extract_references = Mock()
    mock.llm_service = Mock()
    mock.llm_service.stream_response = AsyncMock()
    mock._generate_follow_ups = AsyncMock()
    return mock


@pytest.fixture
def valid_jwt_token():
    """Create a valid JWT token for testing"""
    return jwt_auth.create_access_token({"sub": "test-user-123", "email": "<EMAIL>"})


class TestWebSocketAPI:

    def test_websocket_connection_no_auth(self, test_client):
        """Test WebSocket connection without authentication should fail"""
        with test_client.websocket_connect("/api/v1/ws/query") as websocket:
            # Should receive authentication required error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"
            assert "Authentication required" in data["message"]

    def test_websocket_connection_with_invalid_token(self, test_client):
        """Test WebSocket connection with invalid token should fail"""
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": "Bearer invalid-token"}
        ) as websocket:
            # Should receive authentication required error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"
            assert "Authentication required" in data["message"]

    def test_websocket_connection_with_valid_token(self, test_client, valid_jwt_token):
        """Test WebSocket connection with valid token should succeed"""
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Connection should be established and ready for messages
            assert websocket is not None

    @pytest.mark.skip(reason="WebSocket integration test needs better mocking")
    def test_websocket_query_success(self, test_client, mock_query_processor):
        # Setup mocks
        mock_context = Mock()
        mock_query_processor._create_context.return_value = mock_context

        mock_intent = IntentAnalysis(
            primary_intent=QueryIntent.EXPLAIN,
            code_elements=["auth"],
            scope="repository",
            context_depth="normal",
            confidence=0.9,
        )
        mock_query_processor._analyze_intent.return_value = mock_intent

        mock_query_processor.semantic_search.generate_embedding.return_value = [
            0.1,
            0.2,
            0.3,
        ]

        mock_chunks = [
            CodeChunk(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                content="auth code",
                language="python",
                similarity_score=0.9,
                recency_score=0.8,
                combined_score=0.85,
            )
        ]
        mock_query_processor.semantic_search.search.return_value = SearchResult(
            chunks=mock_chunks, total_results=1, search_time_ms=50.0
        )

        mock_query_processor._build_search_filters.return_value = {}
        mock_query_processor._rerank_chunks.return_value = mock_chunks

        mock_references = [
            CodeReference(
                file_path="src/auth.py",
                start_line=10,
                end_line=20,
                snippet="auth code",
                relevance_score=0.85,
            )
        ]
        mock_query_processor._extract_references.return_value = mock_references

        # Setup streaming response
        async def mock_stream():
            yield "Authentication works by "
            yield "validating JWT tokens..."

        mock_query_processor.llm_service.stream_response.return_value = mock_stream()
        mock_query_processor._generate_follow_ups.return_value = [
            "How are tokens validated?"
        ]

        with patch(
            "query_intelligence.api.websocket.get_query_processor",
            return_value=mock_query_processor,
        ):
            with test_client.websocket_connect("/api/v1/ws/query") as websocket:
                # Send query
                websocket.send_text(
                    json.dumps(
                        {
                            "query": "How does authentication work?",
                            "repository_id": "test-repo",
                        }
                    )
                )

                # Receive acknowledgment
                data = websocket.receive_json()
                assert data["type"] == "acknowledged"
                assert data["query"] == "How does authentication work?"

                # Receive processing started
                data = websocket.receive_json()
                assert data["type"] == "processing_started"

                # Receive intent analysis
                data = websocket.receive_json()
                assert data["type"] == "intent_analyzed"
                assert data["intent"] == "explain"
                assert data["confidence"] == 0.9

                # Receive search status
                data = websocket.receive_json()
                assert data["type"] == "status"
                assert "Searching" in data["message"]

                # Receive search complete
                data = websocket.receive_json()
                assert data["type"] == "search_complete"
                assert data["results_found"] == 1

                # Receive reference
                data = websocket.receive_json()
                assert data["type"] == "reference"
                assert data["reference"]["file_path"] == "src/auth.py"

                # Receive generation status
                data = websocket.receive_json()
                assert data["type"] == "status"
                assert "Generating" in data["message"]

                # Receive text chunks
                data = websocket.receive_json()
                assert data["type"] == "text"
                assert data["content"] == "Authentication works by "

                data = websocket.receive_json()
                assert data["type"] == "text"
                assert data["content"] == "validating JWT tokens..."

                # Receive completion
                data = websocket.receive_json()
                assert data["type"] == "done"
                assert data["done"] is True
                assert "follow_up_questions" in data["metadata"]

    def test_websocket_invalid_json(self, test_client):
        with test_client.websocket_connect("/api/v1/ws/query") as websocket:
            # Send invalid JSON
            websocket.send_text("invalid json")

            # Should receive error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert "Invalid JSON" in data["message"]

    def test_websocket_missing_fields(self, test_client):
        with test_client.websocket_connect("/api/v1/ws/query") as websocket:
            # Send incomplete request
            websocket.send_text(
                json.dumps(
                    {
                        "query": "Test query"
                        # Missing repository_id
                    }
                )
            )

            # Should receive error
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert "Query processing error" in data["message"]

    @pytest.mark.skip(reason="WebSocket integration test needs better mocking")
    def test_websocket_processing_error(self, test_client, mock_query_processor):
        # Setup error
        mock_query_processor._analyze_intent.side_effect = Exception(
            "Processing failed"
        )

        with patch(
            "query_intelligence.api.websocket.get_query_processor",
            return_value=mock_query_processor,
        ):
            with test_client.websocket_connect("/api/v1/ws/query") as websocket:
                # Send query
                websocket.send_text(
                    json.dumps({"query": "Test query", "repository_id": "test-repo"})
                )

                # Skip acknowledgment
                websocket.receive_json()

                # Should receive error
                data = websocket.receive_json()
                assert data["type"] == "error"
                assert "Streaming error" in data["message"]

    def test_websocket_auth_header_formats(self, test_client, valid_jwt_token):
        """Test various authorization header formats"""
        # Test missing Bearer prefix
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": valid_jwt_token}
        ) as websocket:
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"

        # Test empty authorization header
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": ""}
        ) as websocket:
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"

        # Test Bearer with no token
        with test_client.websocket_connect(
            "/api/v1/ws/query", 
            headers={"Authorization": "Bearer "}
        ) as websocket:
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert data["code"] == "AUTH_REQUIRED"

    def test_websocket_rate_limiting_headers(self, test_client, valid_jwt_token):
        """Test that authentication uses headers and doesn't expose tokens in URL"""
        # This test ensures tokens are not logged in access logs
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Connection should succeed with header-based auth
            assert websocket is not None
            # No token should be in URL or query parameters
            
    def test_websocket_connection_security_headers(self, test_client, valid_jwt_token):
        """Test WebSocket connection handles security headers properly"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={
                "Authorization": f"Bearer {valid_jwt_token}",
                "Origin": "https://example.com"
            }
        ) as websocket:
            assert websocket is not None

    def test_websocket_message_size_limits(self, test_client, valid_jwt_token):
        """Test WebSocket handles large messages appropriately"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            # Send extremely large query (should be handled gracefully)
            large_query = "a" * 10000  # 10KB query
            websocket.send_text(json.dumps({
                "query": large_query,
                "repository_id": "test-repo"
            }))
            
            # Should receive appropriate error or processing response
            data = websocket.receive_json()
            assert data["type"] in ["error", "acknowledged"]

    def test_websocket_concurrent_connections(self, test_client, valid_jwt_token):
        """Test multiple WebSocket connections with same user"""
        connections = []
        
        try:
            # Create multiple connections
            for i in range(3):
                conn = test_client.websocket_connect(
                    "/api/v1/ws/query",
                    headers={"Authorization": f"Bearer {valid_jwt_token}"}
                )
                connections.append(conn.__enter__())
            
            # All connections should be valid
            assert len(connections) == 3
            for conn in connections:
                assert conn is not None
                
        finally:
            # Clean up connections
            for conn in connections:
                try:
                    conn.__exit__(None, None, None)
                except:
                    pass

    def test_websocket_malformed_json_messages(self, test_client, valid_jwt_token):
        """Test WebSocket handling of various malformed JSON"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            
            # Test completely invalid JSON
            websocket.send_text("{invalid json")
            data = websocket.receive_json()
            assert data["type"] == "error"
            assert "Invalid JSON" in data["message"]
            
            # Test empty message
            websocket.send_text("")
            data = websocket.receive_json()
            assert data["type"] == "error"
            
            # Test null message
            websocket.send_text("null")
            data = websocket.receive_json()
            assert data["type"] == "error"

    def test_websocket_user_context_isolation(self, test_client):
        """Test that different users have isolated contexts"""
        # Create tokens for different users
        token1 = jwt_auth.create_access_token({"sub": "user1", "email": "<EMAIL>"})
        token2 = jwt_auth.create_access_token({"sub": "user2", "email": "<EMAIL>"})
        
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {token1}"}
        ) as ws1:
            with test_client.websocket_connect(
                "/api/v1/ws/query",
                headers={"Authorization": f"Bearer {token2}"}
            ) as ws2:
                # Both connections should be isolated
                assert ws1 is not None
                assert ws2 is not None

    def test_websocket_reconnection_scenarios(self, test_client, valid_jwt_token):
        """Test WebSocket reconnection behavior"""
        # Test normal connection followed by reconnection
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            assert websocket is not None
            
        # Reconnect should work
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket2:
            assert websocket2 is not None

    def test_websocket_query_validation(self, test_client, valid_jwt_token):
        """Test WebSocket query validation"""
        with test_client.websocket_connect(
            "/api/v1/ws/query",
            headers={"Authorization": f"Bearer {valid_jwt_token}"}
        ) as websocket:
            
            # Test query without repository_id
            websocket.send_text(json.dumps({"query": "test query"}))
            data = websocket.receive_json()
            assert data["type"] == "error"
            
            # Test empty query
            websocket.send_text(json.dumps({
                "query": "",
                "repository_id": "test-repo"
            }))
            data = websocket.receive_json()
            assert data["type"] == "error"
