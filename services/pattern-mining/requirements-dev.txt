# Pattern Mining Service - Development Dependencies
# These are only needed for development and testing
# Install with: pip install -r requirements-dev.txt

# Testing Framework
pytest==8.3.0
pytest-asyncio==0.24.0
pytest-cov==6.0.0
pytest-benchmark==5.0.0
pytest-mock==3.14.0
pytest-timeout==2.3.1
pytest-xdist==3.6.1  # Parallel test execution
hypothesis==6.115.0  # Property-based testing
faker==30.1.0  # Test data generation

# Code Quality & Linting
black==24.10.0
ruff==0.7.2  # Ultra-fast Python linter
mypy==1.12.0
pyright==1.1.385
isort==5.13.0
pre-commit==4.0.0
flake8==7.1.1  # Additional linting
pylint==3.3.1  # Comprehensive linting

# Documentation
mkdocs==1.6.0
mkdocs-material==9.5.40
mkdocstrings[python]==0.26.0
mkdocs-mermaid2-plugin==1.1.1
mkdocs-git-revision-date-localized-plugin==1.2.9

# Type Stubs
types-redis==4.6.0
types-requests==2.32.0
types-PyYAML==6.0.12
types-aiofiles==24.1.0
types-python-dateutil==2.9.0.20241206

# Development Tools
ipython==8.29.0
jupyter==1.1.1
notebook==7.3.1
jupyterlab==4.3.3
ipdb==0.13.13  # Interactive debugger
watchdog==6.0.0  # File system monitoring
python-dotenv[cli]==1.0.1  # .env management

# Profiling & Performance
py-spy==0.4.0  # Sampling profiler
memory-profiler==0.61.0
line-profiler==4.2.0
scalene==1.5.50  # High-performance profiler

# Security Testing
bandit[toml]==1.8.0
safety==3.3.0
pip-audit==2.7.3
semgrep==1.90.0  # Static analysis

# API Testing
httpx[cli]==0.28.0  # HTTP client with CLI
locust==2.32.4  # Load testing
responses==0.25.3  # Mock HTTP responses

# Database Tools
alembic[tz]==1.14.0  # Database migrations
sqlalchemy-utils==0.41.2
faker-sqlalchemy==0.16.0

# Debugging & Monitoring
rich==13.9.0  # Beautiful terminal output
icecream==2.1.3  # Better debugging
loguru==0.7.2  # Enhanced logging
python-json-logger==3.2.1

# Build & Packaging
build==1.2.2
wheel==0.44.0
setuptools>=70.0.0
twine==6.0.1  # Package publishing

# Git Hooks
pre-commit==4.0.0
commitizen==3.30.0  # Conventional commits
gitlint==0.19.1

# Additional Development Utilities
python-decouple==3.8  # Settings management
environs==11.0.0  # Environment variable parsing
dynaconf==3.2.8  # Configuration management
pydantic-extra-types==2.10.1  # Extra Pydantic types